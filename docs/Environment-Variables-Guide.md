# Environment Variables Configuration Guide

This guide explains how to configure the Curio API using environment variables for different environments.

## Overview

The Curio API uses a simplified configuration approach:
- **appsettings.json**: Contains all non-sensitive configuration
- **Environment Variables**: Contains all sensitive information (passwords, keys, etc.)
- **.env files**: For local development convenience

## Configuration Priority

Configuration values are loaded in this order (highest priority first):
1. **Environment Variables** (highest priority)
2. **appsettings.json** (lowest priority)

## Environment Variable Naming

.NET Core uses `__` (double underscore) to represent configuration hierarchy:

```bash
# JSON structure:
{
  "Database": {
    "Password": "secret"
  }
}

# Environment variable:
DATABASE__PASSWORD=secret
```

## Required Environment Variables

### Database Configuration
```bash
# Required
DATABASE__PASSWORD=your-database-password

# Optional (overrides appsettings.json values)
DATABASE__HOST=localhost
DATABASE__PORT=5432
DATABASE__DATABASE=curio
DATABASE__USERNAME=curio
```

### Security Configuration
```bash
# JWT Configuration (Required)
APPLICATION__SECURITY__JWT__SECRETKEY=your-jwt-secret-key-must-be-at-least-32-characters-long

# Encryption Configuration (Required)
APPLICATION__SECURITY__ENCRYPTION__KEY=your-encryption-key-32-characters
APPLICATION__SECURITY__ENCRYPTION__SALT=your-salt-16-chars
```

### Email Configuration (Optional)
```bash
# SMTP Configuration
EMAIL__SMTP__USERNAME=<EMAIL>
EMAIL__SMTP__PASSWORD=your-app-password

# Sender Configuration
EMAIL__DEFAULTSENDER__FROMEMAIL=<EMAIL>
EMAIL__DEFAULTSENDER__REPLYTOEMAIL=<EMAIL>
```

### Kafka Configuration (Optional)
```bash
# Only needed if using SASL authentication
KAFKA__SASLUSERNAME=your-kafka-username
KAFKA__SASLPASSWORD=your-kafka-password
```

### Orleans Configuration (Optional)
```bash
# Connection strings (if empty, uses database connection string)
ORLEANS__CLUSTERING__CONNECTIONSTRING=your-orleans-clustering-connection
ORLEANS__STORAGE__CONNECTIONSTRING=your-orleans-storage-connection
ORLEANS__STREAMING__CONNECTIONSTRING=your-orleans-streaming-connection
ORLEANS__REMINDERS__CONNECTIONSTRING=your-orleans-reminders-connection
```

## Local Development Setup

### Step 1: Copy Environment Template
```bash
cp src/Curio.Api/.env.example src/Curio.Api/.env
```

### Step 2: Edit .env File
Edit the `.env` file with your local configuration:

```bash
# Database
DATABASE__PASSWORD=your-local-db-password

# Security
APPLICATION__SECURITY__JWT__SECRETKEY=your-local-jwt-secret-32-chars-minimum
APPLICATION__SECURITY__ENCRYPTION__KEY=your-local-encryption-key-32-chars
APPLICATION__SECURITY__ENCRYPTION__SALT=your-local-salt-16

# Email (optional for local development)
EMAIL__SMTP__USERNAME=<EMAIL>
EMAIL__SMTP__PASSWORD=your-app-password
EMAIL__DEFAULTSENDER__FROMEMAIL=<EMAIL>
```

### Step 3: Load Environment Variables
The .NET application will automatically load environment variables. For .env file support, you can use:

```bash
# Option 1: Use dotenv CLI tool
dotenv -f src/Curio.Api/.env -- dotnet run --project src/Curio.Api

# Option 2: Export variables manually
export $(cat src/Curio.Api/.env | xargs)
dotnet run --project src/Curio.Api
```

## Production Deployment

### GitHub Environments Setup

1. **Create GitHub Environments**:
   - Go to repository Settings → Environments
   - Create `development` environment
   - Create `production` environment

2. **Configure Environment Secrets**:

**Development Environment:**
```
DATABASE__PASSWORD=dev-db-password
APPLICATION__SECURITY__JWT__SECRETKEY=dev-jwt-secret-32-chars
APPLICATION__SECURITY__ENCRYPTION__KEY=dev-encryption-key-32-chars
APPLICATION__SECURITY__ENCRYPTION__SALT=dev-salt-16
EMAIL__SMTP__USERNAME=<EMAIL>
EMAIL__SMTP__PASSWORD=dev-email-password
```

**Production Environment:**
```
DATABASE__PASSWORD=prod-db-password
APPLICATION__SECURITY__JWT__SECRETKEY=prod-jwt-secret-32-chars
APPLICATION__SECURITY__ENCRYPTION__KEY=prod-encryption-key-32-chars
APPLICATION__SECURITY__ENCRYPTION__SALT=prod-salt-16
EMAIL__SMTP__USERNAME=<EMAIL>
EMAIL__SMTP__PASSWORD=prod-email-password
```

### Kubernetes Deployment

Environment variables can be injected via:

1. **ConfigMaps** (for non-sensitive data):
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: curio-config
data:
  DATABASE__HOST: "postgres-service"
  DATABASE__PORT: "5432"
  DATABASE__DATABASE: "curio"
```

2. **Secrets** (for sensitive data):
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: curio-secrets
type: Opaque
data:
  DATABASE__PASSWORD: <base64-encoded-password>
  APPLICATION__SECURITY__JWT__SECRETKEY: <base64-encoded-jwt-key>
```

## Environment-Specific Configurations

### Local Docker Setup
```bash
# .env.docker
DATABASE__HOST=localhost
DATABASE__PORT=5432
KAFKA__BROKERLIST__0=localhost:9092
ORLEANS__CLUSTERID=curio-cluster-docker
```

### Local Cloud Setup
```bash
# .env.cloud
DATABASE__HOST=your-cloud-db.amazonaws.com
DATABASE__PORT=5432
KAFKA__BROKERLIST__0=your-kafka-cluster.amazonaws.com:9092
ORLEANS__CLUSTERID=curio-cluster-cloud
```

## Validation

The application validates required configuration on startup. Missing required environment variables will cause the application to fail with clear error messages.

## Security Best Practices

1. **Never commit .env files** to version control
2. **Use strong, unique secrets** for each environment
3. **Rotate secrets regularly** in production
4. **Use GitHub Environments** to control deployment access
5. **Monitor configuration changes** in production

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**: Check application logs for validation errors
2. **Wrong Variable Names**: Ensure double underscores `__` are used correctly
3. **Connection Issues**: Verify database/service connectivity with provided credentials

### Debug Configuration
In development, the application prints a configuration summary showing which values are loaded from which sources.
