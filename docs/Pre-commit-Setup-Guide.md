# Pre-commit Hooks Setup Guide

This document explains how to set up and use pre-commit hooks for the Curio API project.

## Overview

Pre-commit hooks are scripts that run automatically before each commit to ensure code quality, consistency, and security. This project includes hooks for:

- **Code Formatting**: Automatic .NET code formatting using `dotnet format`
- **Build Validation**: Ensures the project builds successfully
- **Test Execution**: Runs unit tests before commits
- **Security Scanning**: Detects potential secrets and sensitive information
- **File Validation**: Checks for common issues like trailing whitespace, large files, etc.

## Quick Setup

Run the setup script to install and configure everything automatically:

```bash
./scripts/setup-pre-commit.sh
```

## Manual Setup

If you prefer to set up manually or the script doesn't work:

### Prerequisites

1. **Python 3.x** with pip
2. **.NET SDK** (version 6.0 or later)
3. **Git**

### Installation Steps

1. Install pre-commit:
   ```bash
   pip install pre-commit
   ```

2. Install .NET format tool:
   ```bash
   dotnet tool install -g dotnet-format
   ```

3. Install detect-secrets:
   ```bash
   pip install detect-secrets
   ```

4. Install pre-commit hooks:
   ```bash
   pre-commit install
   ```

5. Generate secrets baseline:
   ```bash
   detect-secrets scan --baseline .secrets.baseline
   ```

## Configuration Files

The following files configure the pre-commit behavior:

### `.pre-commit-config.yaml`
Main configuration file defining which hooks to run and their settings.

### `.secrets.baseline`
Baseline file for the secrets detection tool to avoid false positives.

### `Directory.Build.props`
MSBuild properties file that enables code analysis and sets project-wide standards.

### `.editorconfig`
Defines coding style and formatting rules for the IDE and tools.

### `.gitattributes`
Ensures consistent line endings and file handling across different platforms.

### `.markdownlint.json`
Configuration for markdown linting (if markdownlint is available).

## Available Hooks

### Automatic Hooks (run on every commit)

1. **trailing-whitespace**: Removes trailing whitespace
2. **end-of-file-fixer**: Ensures files end with a newline
3. **check-yaml**: Validates YAML syntax
4. **check-json**: Validates JSON syntax
5. **check-xml**: Validates XML syntax
6. **check-merge-conflict**: Detects merge conflict markers
7. **check-case-conflict**: Detects case conflicts in filenames
8. **check-added-large-files**: Prevents committing large files (>1MB)
9. **mixed-line-ending**: Fixes mixed line endings
10. **detect-secrets**: Scans for potential secrets
11. **dotnet-format**: Formats .NET code according to style rules
12. **dotnet-build**: Builds the solution to ensure it compiles
13. **dotnet-test**: Runs unit tests

## Manual Commands

You can run hooks manually without committing:

```bash
# Run all hooks on all files
pre-commit run --all-files

# Run specific hook
pre-commit run dotnet-format
pre-commit run dotnet-build
pre-commit run detect-secrets

# Format code manually
dotnet format

# Build solution manually
dotnet build

# Run tests manually
dotnet test
```

## Skipping Hooks

Sometimes you may need to skip hooks (use sparingly):

```bash
# Skip all hooks
git commit --no-verify -m "commit message"

# Skip specific hooks using SKIP environment variable
SKIP=dotnet-test git commit -m "commit message"
```

## Updating Hooks

Keep hooks up to date:

```bash
# Update to latest versions
pre-commit autoupdate

# Clean and reinstall hooks
pre-commit clean
pre-commit install
```

## Troubleshooting

### Common Issues

1. **dotnet-format fails**: Ensure .NET SDK is installed and in PATH
2. **detect-secrets fails**: Update the `.secrets.baseline` file
3. **Tests fail**: Fix failing tests before committing
4. **Build fails**: Fix compilation errors

### Regenerating Secrets Baseline

If you get false positives from secrets detection:

```bash
detect-secrets scan --baseline .secrets.baseline
```

### Performance Issues

If hooks are too slow:

1. Skip tests in commits: `SKIP=dotnet-test git commit -m "message"`
2. Run tests separately: `dotnet test`
3. Consider using `--no-build` flags in test commands

## Best Practices

1. **Run hooks locally** before pushing to catch issues early
2. **Keep hooks updated** with `pre-commit autoupdate`
3. **Don't skip hooks** unless absolutely necessary
4. **Fix issues** rather than bypassing hooks
5. **Update baseline files** when legitimate changes occur

## Integration with CI/CD

The pre-commit configuration includes CI settings that work with:

- **GitHub Actions**: Use `pre-commit/action@v3.0.0`
- **Azure DevOps**: Install pre-commit in pipeline
- **GitLab CI**: Add pre-commit to `.gitlab-ci.yml`

Example GitHub Action:

```yaml
- uses: pre-commit/action@v3.0.0
  with:
    extra_args: --all-files
```

## Support

For issues with pre-commit setup:

1. Check the [pre-commit documentation](https://pre-commit.com/)
2. Review project-specific configuration files
3. Run `pre-commit run --all-files` to test setup
4. Check tool-specific documentation (.NET, detect-secrets, etc.)
