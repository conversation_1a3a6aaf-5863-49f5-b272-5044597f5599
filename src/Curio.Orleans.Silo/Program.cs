﻿﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Orleans;
using Orleans.Configuration;
using Orleans.Hosting;
using Orleans.Streams;
using Orleans.Streams.Kafka.Config;
using Curio.Infrastructure;
using Curio.Infrastructure.Configuration;
using Curio.Infrastructure.Services;
using Curio.Orleans.Grains;
using Curio.Orleans.Interfaces;

var builder = Host.CreateDefaultBuilder(args)
    .ConfigureServices((context, services) =>
    {
        // Register infrastructure services
        services.AddInfrastructureServices(context.Configuration);
    })
    .ConfigureAppConfiguration((context, config) =>
    {
        // 清除可能导致冲突的配置节
        config.Sources.Clear();
        config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
        config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true, reloadOnChange: true);
        config.AddEnvironmentVariables();
    });

// Configure Orleans Silo
builder.UseOrleans((context, siloBuilder) =>
{
    // Get configuration settings
    var orleansSettings = context.Configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();
    var kafkaSettings = context.Configuration.GetSection(KafkaSettings.SectionName).Get<KafkaSettings>() ?? new KafkaSettings();

    // Get connection strings using extension methods
    var clusteringConnectionString = context.Configuration.GetOrleansConnectionString("clustering");
    var storageConnectionString = context.Configuration.GetOrleansConnectionString("storage");
    var remindersConnectionString = context.Configuration.GetOrleansConnectionString("reminders");
    var kafkaBrokers = context.Configuration.GetKafkaBrokersString();

    // Debug: Print connection strings
    Console.WriteLine($"Debug - Clustering Connection: {clusteringConnectionString}");
    Console.WriteLine($"Debug - Storage Connection: {storageConnectionString}");
    Console.WriteLine($"Debug - Reminders Connection: {remindersConnectionString}");
    siloBuilder
        // Explicit configuration to avoid auto-discovery conflicts
        .ConfigureServices(services =>
        {
            // Clear possible auto-configurations
        })

        // Cluster configuration - using PostgreSQL clustering
        .UseAdoNetClustering(options =>
        {
            options.ConnectionString = clusteringConnectionString;
            options.Invariant = "Npgsql";
        })
        .Configure<ClusterOptions>(options =>
        {
            options.ClusterId = orleansSettings.ClusterId;
            options.ServiceId = orleansSettings.ServiceId;
        })

        // Orleans state storage configuration
        .AddAdoNetGrainStorage("Default", options =>
        {
            options.ConnectionString = storageConnectionString;
            options.Invariant = "Npgsql";
        })
        .AddAdoNetGrainStorage("JournaledGrainState", options =>
        {
            options.ConnectionString = storageConnectionString;
            options.Invariant = "Npgsql";
        })

        // JournaledGrain Event Sourcing configuration
        .AddLogStorageBasedLogConsistencyProvider("EventSourcing")

        // Kafka Streams configuration
        .AddKafka("KafkaStreams")
        .WithOptions(options =>
        {
            options.BrokerList = kafkaSettings.BrokerList;
            options.ConsumerGroupId = kafkaSettings.ConsumerGroupId;

            // Topics configuration - 避免重复添加主题
            var uniqueTopics = kafkaSettings.Topics.Distinct().ToList();
            foreach (var topic in uniqueTopics)
            {
                options.AddTopic(topic);
            }
        })
        .AddJson()
        .Build()

        // PubSub storage
        .AddMemoryGrainStorage("PubSubStore")

        // Orleans Reminders (scheduled tasks)
        .UseAdoNetReminderService(options =>
        {
            options.ConnectionString = remindersConnectionString;
            options.Invariant = "Npgsql";
        })

        // Configure logging
        .ConfigureLogging(logging => logging.AddConsole())

        // Configure Orleans internal services
        .ConfigureServices(services =>
        {
            // Note: IResilientEventPublisher is not registered here
            // It is created in each Grain's OnActivateAsync because it needs access to IStreamProvider
            // IStreamProvider is only available in the Grain context, not in the DI container
        });
});

var app = builder.Build();

// Start Orleans Silo
var configuration = app.Services.GetRequiredService<IConfiguration>();
var orleansSettings = configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();
var kafkaBrokers = configuration.GetKafkaBrokersString();

Console.WriteLine("🚀 Starting Curio Orleans Silo...");
Console.WriteLine($"📊 Cluster: {orleansSettings.ClusterId}");
Console.WriteLine($"🏷️  Service: {orleansSettings.ServiceId}");
Console.WriteLine($"🗄️  Database: PostgreSQL");
Console.WriteLine($"🔗 Clustering: PostgreSQL ADO.NET");
Console.WriteLine($"📨 Kafka: {kafkaBrokers}");
Console.WriteLine($"⚡ Event Sourcing: Enabled with Kafka streaming");
Console.WriteLine();

await app.RunAsync();
