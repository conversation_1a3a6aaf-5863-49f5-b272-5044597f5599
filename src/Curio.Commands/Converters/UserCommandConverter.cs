using Curio.Shared.Users;
using Curio.Events.Commands;

namespace Curio.Commands.Converters;

/// <summary>
/// 用户命令转换器 - 将Web层请求对象转换为领域层命令对象
/// 实现关注点分离和分层架构原则
/// </summary>
public static class UserCommandConverter
{
    /// <summary>
    /// 将Web注册命令转换为领域注册命令
    /// </summary>
    public static RegisterUserDomainCommand ToDomainCommand(this RegisterUserCommand webCommand, string? requestSource = null)
    {
        if (webCommand == null) throw new ArgumentNullException(nameof(webCommand));
        
        return new RegisterUserDomainCommand
        {
            Email = webCommand.Email?.Trim().ToLowerInvariant() ?? string.Empty,
            Name = webCommand.Name?.Trim() ?? string.Empty,
            CommandId = !string.IsNullOrEmpty(webCommand.CommandId) ? webCommand.CommandId : Guid.NewGuid().ToString(),
            RequestedAt = DateTime.UtcNow,
            RequestSource = requestSource ?? "web-api"
        };
    }

    /// <summary>
    /// 将Web登录命令转换为领域登录命令
    /// </summary>
    public static LoginUserDomainCommand ToDomainCommand(this LoginUserCommand webCommand, string? requestSource = null)
    {
        if (webCommand == null) throw new ArgumentNullException(nameof(webCommand));
        
        return new LoginUserDomainCommand
        {
            Email = webCommand.Email?.Trim().ToLowerInvariant() ?? string.Empty,
            CommandId = !string.IsNullOrEmpty(webCommand.CommandId) ? webCommand.CommandId : Guid.NewGuid().ToString(),
            RequestedAt = DateTime.UtcNow,
            RequestSource = requestSource ?? "web-api"
        };
    }

    /// <summary>
    /// 将Web邮箱检查命令转换为领域命令
    /// </summary>
    public static CheckUserExistsDomainCommand ToDomainCommand(this CheckEmailExistsCommand webCommand, string? requestSource = null)
    {
        if (webCommand == null) throw new ArgumentNullException(nameof(webCommand));
        
        return new CheckUserExistsDomainCommand
        {
            Email = webCommand.Email?.Trim().ToLowerInvariant() ?? string.Empty,
            CommandId = !string.IsNullOrEmpty(webCommand.CommandId) ? webCommand.CommandId : Guid.NewGuid().ToString(),
            RequestedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 验证领域命令的有效性
    /// </summary>
    public static void ValidateDomainCommand(this RegisterUserDomainCommand command)
    {
        if (string.IsNullOrWhiteSpace(command.Email))
            throw new ArgumentException("Email is required", nameof(command.Email));
            
        if (string.IsNullOrWhiteSpace(command.Name))
            throw new ArgumentException("Name is required", nameof(command.Name));
            
        if (!IsValidEmail(command.Email))
            throw new ArgumentException("Invalid email format", nameof(command.Email));
    }

    /// <summary>
    /// 验证领域命令的有效性
    /// </summary>
    public static void ValidateDomainCommand(this LoginUserDomainCommand command)
    {
        if (string.IsNullOrWhiteSpace(command.Email))
            throw new ArgumentException("Email is required", nameof(command.Email));
            
        if (!IsValidEmail(command.Email))
            throw new ArgumentException("Invalid email format", nameof(command.Email));
    }

    /// <summary>
    /// 验证领域命令的有效性
    /// </summary>
    public static void ValidateDomainCommand(this CheckUserExistsDomainCommand command)
    {
        if (string.IsNullOrWhiteSpace(command.Email))
            throw new ArgumentException("Email is required", nameof(command.Email));
            
        if (!IsValidEmail(command.Email))
            throw new ArgumentException("Invalid email format", nameof(command.Email));
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}