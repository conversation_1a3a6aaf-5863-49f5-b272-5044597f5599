namespace Curio.Commands.Interfaces;

/// <summary>
/// 命令处理器基础接口 - CQRS 命令端
/// 明确的写操作边界，专注于业务命令处理
/// </summary>
/// <typeparam name="TCommand">命令类型</typeparam>
/// <typeparam name="TResult">结果类型</typeparam>
public interface ICommandHandler<in TCommand, TResult>
{
    /// <summary>
    /// 处理命令
    /// </summary>
    /// <param name="command">命令</param>
    /// <returns>处理结果</returns>
    Task<TResult> HandleAsync(TCommand command);
}

/// <summary>
/// 无返回值的命令处理器接口
/// </summary>
/// <typeparam name="TCommand">命令类型</typeparam>
public interface ICommandHandler<in TCommand>
{
    /// <summary>
    /// 处理命令
    /// </summary>
    /// <param name="command">命令</param>
    Task HandleAsync(TCommand command);
}