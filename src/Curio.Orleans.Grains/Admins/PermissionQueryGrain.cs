using Orleans;
using Microsoft.Extensions.Logging;
using Curio.Orleans.Interfaces.Admins;
using Curio.Shared.Admins;
using Curio.Shared.Users;
using System.Collections.Concurrent;

namespace Curio.Orleans.Grains.Admins;

/// <summary>
/// 权限查询Grain实现
/// 提供高效的权限查询和缓存功能
/// </summary>
public class PermissionQueryGrain : Grain, IPermissionQueryGrain
{
    private readonly ILogger<PermissionQueryGrain> _logger;
    private readonly ConcurrentDictionary<string, PermissionCacheDto> _permissionCache = new();
    private readonly ConcurrentDictionary<string, RoleDto> _roleCache = new();
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(30);

    public PermissionQueryGrain(ILogger<PermissionQueryGrain> logger)
    {
        _logger = logger;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);
        _logger.LogInformation("PermissionQueryGrain activated");
        
        // 启动缓存清理定时器
        this.RegisterGrainTimer(
            callback: () => CleanExpiredCacheAsync(null),
            options: new global::Orleans.Runtime.GrainTimerCreationOptions
            {
                DueTime = TimeSpan.FromMinutes(5),
                Period = TimeSpan.FromMinutes(5),
                Interleave = true
            });
    }

    public async Task<bool> HasPermissionAsync(string adminId, PermissionResource resource, PermissionAction action)
    {
        try
        {
            var permissions = await GetPermissionCacheAsync(adminId);
            var permissionKey = $"{resource}:{action}";
            
            return permissions.Permissions.GetValueOrDefault(permissionKey, false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission for admin {AdminId}: {Resource}:{Action}", adminId, resource, action);
            return false;
        }
    }

    public async Task<List<PermissionDto>> GetAdminPermissionsAsync(string adminId)
    {
        try
        {
            var permissions = new List<PermissionDto>();
            
            // 获取管理员信息
            var adminGrain = GrainFactory.GetGrain<IAdminGrain>(adminId);
            var admin = await adminGrain.GetAdminAsync();
            
            if (admin == null)
            {
                return permissions;
            }

            // 获取角色权限
            foreach (var roleId in admin.RoleIds)
            {
                var rolePermissions = await GetRolePermissionsAsync(roleId);
                foreach (var permission in rolePermissions)
                {
                    permission.GrantedByRole = roleId;
                    permissions.Add(permission);
                }
            }

            // 去重并返回
            return permissions
                .GroupBy(p => new { p.Resource, p.Action })
                .Select(g => g.First())
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin permissions: {AdminId}", adminId);
            return new List<PermissionDto>();
        }
    }

    public async Task<List<RoleDto>> GetAdminRolesAsync(string adminId)
    {
        try
        {
            var roles = new List<RoleDto>();
            
            // 获取管理员信息
            var adminGrain = GrainFactory.GetGrain<IAdminGrain>(adminId);
            var admin = await adminGrain.GetAdminAsync();
            
            if (admin == null)
            {
                return roles;
            }

            // 获取角色信息
            foreach (var roleId in admin.RoleIds)
            {
                var role = await GetRoleCacheAsync(roleId);
                if (role != null)
                {
                    roles.Add(role);
                }
            }

            return roles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin roles: {AdminId}", adminId);
            return new List<RoleDto>();
        }
    }

    public async Task<List<PermissionDto>> GetRolePermissionsAsync(string roleId)
    {
        try
        {
            var roleGrain = GrainFactory.GetGrain<IRoleGrain>(roleId);
            return await roleGrain.GetPermissionsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role permissions: {RoleId}", roleId);
            return new List<PermissionDto>();
        }
    }

    public async Task<bool> RoleHasPermissionAsync(string roleId, PermissionResource resource, PermissionAction action)
    {
        try
        {
            var roleGrain = GrainFactory.GetGrain<IRoleGrain>(roleId);
            return await roleGrain.HasPermissionAsync(resource, action);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role permission: {RoleId}", roleId);
            return false;
        }
    }

    public async Task<List<RoleDto>> GetRolesWithPermissionAsync(PermissionResource resource, PermissionAction action)
    {
        try
        {
            var roles = new List<RoleDto>();
            
            // 这里需要实现角色查询逻辑
            // 在实际项目中，可能需要从投影或数据库中查询
            // 临时实现，返回空列表
            await Task.CompletedTask;
            
            return roles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles with permission: {Resource}:{Action}", resource, action);
            return new List<RoleDto>();
        }
    }

    public async Task<List<AdminSummaryDto>> GetAdminsWithRoleAsync(string roleId)
    {
        try
        {
            var admins = new List<AdminSummaryDto>();
            
            // 这里需要实现管理员查询逻辑
            // 在实际项目中，可能需要从投影或数据库中查询
            // 临时实现，返回空列表
            await Task.CompletedTask;
            
            return admins;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admins with role: {RoleId}", roleId);
            return new List<AdminSummaryDto>();
        }
    }

    public async Task RefreshPermissionCacheAsync()
    {
        try
        {
            _permissionCache.Clear();
            _roleCache.Clear();
            _logger.LogInformation("Permission cache refreshed");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing permission cache");
        }
    }

    public async Task<Dictionary<string, bool>> BatchCheckPermissionsAsync(string adminId, List<(PermissionResource resource, PermissionAction action)> permissions)
    {
        try
        {
            var results = new Dictionary<string, bool>();
            var permissionCache = await GetPermissionCacheAsync(adminId);
            
            foreach (var (resource, action) in permissions)
            {
                var permissionKey = $"{resource}:{action}";
                var hasPermission = permissionCache.Permissions.GetValueOrDefault(permissionKey, false);
                results[permissionKey] = hasPermission;
            }
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch checking permissions for admin: {AdminId}", adminId);
            return new Dictionary<string, bool>();
        }
    }

    public async Task<List<PermissionInheritanceDto>> GetPermissionInheritanceAsync(string adminId)
    {
        try
        {
            var inheritanceList = new List<PermissionInheritanceDto>();
            
            // 获取管理员角色
            var roles = await GetAdminRolesAsync(adminId);
            
            // 获取所有可能的权限
            var allPermissions = GetAllPossiblePermissions();
            
            foreach (var permission in allPermissions)
            {
                var inheritance = new PermissionInheritanceDto
                {
                    Resource = permission.resource,
                    Action = permission.action,
                    ResourceName = GetResourceDisplayName(permission.resource),
                    ActionName = GetActionDisplayName(permission.action),
                    Sources = new List<PermissionSourceDto>(),
                    IsGranted = false
                };
                
                // 检查每个角色是否有此权限
                foreach (var role in roles)
                {
                    var hasPermission = await RoleHasPermissionAsync(role.Id, permission.resource, permission.action);
                    if (hasPermission)
                    {
                        inheritance.IsGranted = true;
                        inheritance.Sources.Add(new PermissionSourceDto
                        {
                            SourceType = "Role",
                            SourceId = role.Id,
                            SourceName = role.Name,
                            GrantedAt = DateTime.UtcNow, // 这里应该从实际数据获取
                            GrantedBy = role.CreatedBy
                        });
                        
                        if (!inheritance.GrantedAt.HasValue)
                        {
                            inheritance.GrantedAt = DateTime.UtcNow; // 这里应该从实际数据获取
                        }
                    }
                }
                
                inheritanceList.Add(inheritance);
            }
            
            return inheritanceList.Where(i => i.IsGranted).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permission inheritance for admin: {AdminId}", adminId);
            return new List<PermissionInheritanceDto>();
        }
    }

    // 私有辅助方法

    private async Task<PermissionCacheDto> GetPermissionCacheAsync(string adminId)
    {
        if (_permissionCache.TryGetValue(adminId, out var cached) && cached.ExpiresAt > DateTime.UtcNow)
        {
            return cached;
        }

        // 重新构建缓存
        var permissions = await BuildPermissionCacheAsync(adminId);
        _permissionCache[adminId] = permissions;
        return permissions;
    }

    private async Task<PermissionCacheDto> BuildPermissionCacheAsync(string adminId)
    {
        var cache = new PermissionCacheDto
        {
            AdminId = adminId,
            Permissions = new Dictionary<string, bool>(),
            RoleIds = new List<string>(),
            LastUpdated = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.Add(_cacheExpiration)
        };

        try
        {
            // 获取管理员信息
            var adminGrain = GrainFactory.GetGrain<IAdminGrain>(adminId);
            var admin = await adminGrain.GetAdminAsync();
            
            if (admin == null)
            {
                return cache;
            }

            cache.RoleIds = admin.RoleIds.ToList();

            // 获取所有角色的权限
            var allPermissions = new HashSet<string>();
            
            foreach (var roleId in admin.RoleIds)
            {
                var rolePermissions = await GetRolePermissionsAsync(roleId);
                foreach (var permission in rolePermissions)
                {
                    var permissionKey = $"{permission.Resource}:{permission.Action}";
                    allPermissions.Add(permissionKey);
                }
            }

            // 构建权限字典
            foreach (var permissionKey in allPermissions)
            {
                cache.Permissions[permissionKey] = true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error building permission cache for admin: {AdminId}", adminId);
        }

        return cache;
    }

    private async Task<RoleDto?> GetRoleCacheAsync(string roleId)
    {
        if (_roleCache.TryGetValue(roleId, out var cached))
        {
            return cached;
        }

        try
        {
            var roleGrain = GrainFactory.GetGrain<IRoleGrain>(roleId);
            var role = await roleGrain.GetRoleAsync();
            
            if (role != null)
            {
                _roleCache[roleId] = role;
            }
            
            return role;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role from cache: {RoleId}", roleId);
            return null;
        }
    }

    private Task CleanExpiredCacheAsync(object? state)
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredKeys = _permissionCache
                .Where(kvp => kvp.Value.ExpiresAt <= now)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _permissionCache.TryRemove(key, out _);
            }

            if (expiredKeys.Any())
            {
                _logger.LogDebug("Cleaned {Count} expired permission cache entries", expiredKeys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning expired cache");
        }

        return Task.CompletedTask;
    }

    private static List<(PermissionResource resource, PermissionAction action)> GetAllPossiblePermissions()
    {
        var permissions = new List<(PermissionResource, PermissionAction)>();
        
        foreach (PermissionResource resource in Enum.GetValues<PermissionResource>())
        {
            foreach (PermissionAction action in Enum.GetValues<PermissionAction>())
            {
                permissions.Add((resource, action));
            }
        }
        
        return permissions;
    }

    private static string GetResourceDisplayName(PermissionResource resource)
    {
        return resource switch
        {
            PermissionResource.Users => "用户管理",
            PermissionResource.Admins => "管理员管理",
            PermissionResource.Roles => "角色管理",
            PermissionResource.Permissions => "权限管理",
            PermissionResource.SystemConfig => "系统配置",
            PermissionResource.AuditLogs => "审计日志",
            PermissionResource.EmailTemplates => "邮件模板",
            PermissionResource.Monitoring => "系统监控",
            PermissionResource.ProjectionManagement => "投影管理",
            PermissionResource.DeadLetterQueue => "死信队列管理",
            _ => resource.ToString()
        };
    }

    private static string GetActionDisplayName(PermissionAction action)
    {
        return action switch
        {
            PermissionAction.Read => "查看",
            PermissionAction.Create => "创建",
            PermissionAction.Update => "更新",
            PermissionAction.Delete => "删除",
            PermissionAction.Execute => "执行",
            PermissionAction.Manage => "管理",
            _ => action.ToString()
        };
    }
}