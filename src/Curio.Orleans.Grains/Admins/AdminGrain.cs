using Orleans;
using Orleans.EventSourcing;
using Microsoft.Extensions.Logging;
using Curio.Orleans.Interfaces.Admins;
using Curio.Shared.Admins;
using Curio.Shared.Users;
using Curio.Orleans.Grains.Base;
using Curio.Infrastructure.Services;
using System.Security.Cryptography;
using System.Text;
using AdminState = Curio.Events.State.AdminState;

namespace Curio.Orleans.Grains.Admins;

public class AdminGrain : ResilientJournaledGrain<AdminState, DomainEvent>, IAdminGrain
{
    private readonly ILogger<AdminGrain> _logger;
    private readonly ITotpService _totpService;

    public AdminGrain(ILogger<AdminGrain> logger, ITotpService totpService)
    {
        _logger = logger;
        _totpService = totpService;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);
        _logger.LogInformation("AdminGrain activated: {AdminId}", this.GetPrimaryKeyString());
    }

    public Task<AdminDto?> GetAdminAsync()
    {
        if (string.IsNullOrEmpty(State.Id))
        {
            return Task.FromResult<AdminDto?>(null);
        }

        return Task.FromResult<AdminDto?>(State.ToDto());
    }

    public async Task<AdminOperationResult> CreateAdminAsync(CreateAdminCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查管理员是否已存在
            if (!string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin already exists",
                    ErrorCode = "ADMIN_EXISTS"
                };
            }

            // 验证输入
            if (string.IsNullOrWhiteSpace(command.Username) ||
                string.IsNullOrWhiteSpace(command.Email) ||
                string.IsNullOrWhiteSpace(command.Name))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Invalid input data",
                    ErrorCode = "INVALID_INPUT"
                };
            }

            // 创建管理员事件
            var adminCreatedEvent = new AdminCreatedEvent
            {
                AdminId = this.GetPrimaryKeyString(),
                Username = command.Username,
                Email = command.Email,
                Name = command.Name,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = command.CreatedBy,
                CommandId = command.CommandId
            };

            // 应用事件
            RaiseEvent(adminCreatedEvent);

            // 如果提供了初始密码，设置密码
            if (!string.IsNullOrWhiteSpace(command.InitialPassword))
            {
                var passwordHash = HashPassword(command.InitialPassword);
                var passwordChangedEvent = new AdminPasswordChangedEvent
                {
                    AdminId = this.GetPrimaryKeyString(),
                    PasswordHash = passwordHash,
                    ChangedAt = DateTime.UtcNow,
                    IsInitialSetup = true,
                    CommandId = command.CommandId
                };

                RaiseEvent(passwordChangedEvent);
            }

            // 分配角色
            foreach (var roleId in command.RoleIds)
            {
                var roleAssignedEvent = new AdminRoleAssignedEvent
                {
                    AdminId = this.GetPrimaryKeyString(),
                    RoleId = roleId,
                    RoleName = string.Empty, // 将由事件处理器填充
                    AssignedAt = DateTime.UtcNow,
                    AssignedBy = command.CreatedBy,
                    CommandId = command.CommandId
                };

                RaiseEvent(roleAssignedEvent);
            }

            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Admin created successfully",
                Admin = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating admin: {Username}", command.Username);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to create admin",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> UpdateAdminAsync(UpdateAdminCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin not found",
                    ErrorCode = "ADMIN_NOT_FOUND"
                };
            }

            // 验证输入
            if (string.IsNullOrWhiteSpace(command.Email) || string.IsNullOrWhiteSpace(command.Name))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Invalid input data",
                    ErrorCode = "INVALID_INPUT"
                };
            }

            // 创建更新事件
            var adminUpdatedEvent = new AdminUpdatedEvent
            {
                AdminId = State.Id,
                Email = command.Email,
                Name = command.Name,
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = command.UpdatedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(adminUpdatedEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Admin updated successfully",
                Admin = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating admin: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to update admin",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> ChangePasswordAsync(ChangeAdminPasswordCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin not found",
                    ErrorCode = "ADMIN_NOT_FOUND"
                };
            }

            // 检查是否可以修改密码
            if (!State.CanChangePassword())
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Cannot change password in current state",
                    ErrorCode = "INVALID_STATE"
                };
            }

            // 验证当前密码（除非是初始设置）
            if (!command.IsInitialSetup && !VerifyPassword(command.CurrentPassword, State.PasswordHash))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Current password is incorrect",
                    ErrorCode = "INVALID_PASSWORD"
                };
            }

            // 创建密码修改事件
            var passwordHash = HashPassword(command.NewPassword);
            var passwordChangedEvent = new AdminPasswordChangedEvent
            {
                AdminId = State.Id,
                PasswordHash = passwordHash,
                ChangedAt = DateTime.UtcNow,
                IsInitialSetup = command.IsInitialSetup,
                CommandId = command.CommandId
            };

            RaiseEvent(passwordChangedEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Password changed successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for admin: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to change password",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<LoginResult> LoginAsync(AdminLoginCommand command)
    {
        try
        {
            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                var notFoundEvent = new AdminLoginAttemptedEvent
                {
                    AdminId = string.Empty,
                    Username = command.Username,
                    Success = false,
                    FailureReason = "Admin not found",
                    IpAddress = command.IpAddress,
                    UserAgent = command.UserAgent,
                    AttemptedAt = DateTime.UtcNow,
                    RequiredTwoFactor = false,
                    CommandId = command.CommandId
                };

                RaiseEvent(notFoundEvent);
                await ConfirmEvents();

                return new LoginResult
                {
                    Success = false,
                    Message = "Invalid credentials"
                };
            }

            // 检查是否可以登录
            if (!State.CanLogin(command.Username, command.Password))
            {
                var cannotLoginEvent = new AdminLoginAttemptedEvent
                {
                    AdminId = State.Id,
                    Username = command.Username,
                    Success = false,
                    FailureReason = "Account locked or disabled",
                    IpAddress = command.IpAddress,
                    UserAgent = command.UserAgent,
                    AttemptedAt = DateTime.UtcNow,
                    RequiredTwoFactor = false,
                    CommandId = command.CommandId
                };

                RaiseEvent(cannotLoginEvent);
                await ConfirmEvents();

                return new LoginResult
                {
                    Success = false,
                    Message = "Account is locked or disabled"
                };
            }

            // 验证密码
            if (!VerifyPassword(command.Password, State.PasswordHash))
            {
                var invalidPasswordEvent = new AdminLoginAttemptedEvent
                {
                    AdminId = State.Id,
                    Username = command.Username,
                    Success = false,
                    FailureReason = "Invalid password",
                    IpAddress = command.IpAddress,
                    UserAgent = command.UserAgent,
                    AttemptedAt = DateTime.UtcNow,
                    RequiredTwoFactor = false,
                    CommandId = command.CommandId
                };

                RaiseEvent(invalidPasswordEvent);
                await ConfirmEvents();

                return new LoginResult
                {
                    Success = false,
                    Message = "Invalid credentials"
                };
            }

            // 检查是否需要2FA
            if (State.RequiresTwoFactor())
            {
                if (string.IsNullOrEmpty(command.TwoFactorCode))
                {
                    // 需要2FA但未提供代码
                    return new LoginResult
                    {
                        Success = false,
                        Message = "Two-factor authentication required",
                        RequiresTwoFactor = true,
                        TwoFactorSessionId = Guid.NewGuid().ToString()
                    };
                }

                // 验证2FA代码（这里需要实现TOTP验证逻辑）
                var is2FAValid = await VerifyTwoFactorCode(command.TwoFactorCode);
                if (!is2FAValid)
                {
                    var invalid2FAEvent = new AdminLoginAttemptedEvent
                    {
                        AdminId = State.Id,
                        Username = command.Username,
                        Success = false,
                        FailureReason = "Invalid two-factor code",
                        IpAddress = command.IpAddress,
                        UserAgent = command.UserAgent,
                        AttemptedAt = DateTime.UtcNow,
                        RequiredTwoFactor = true,
                        CommandId = command.CommandId
                    };

                    RaiseEvent(invalid2FAEvent);
                    await ConfirmEvents();

                    return new LoginResult
                    {
                        Success = false,
                        Message = "Invalid two-factor authentication code"
                    };
                }
            }

            // 登录成功
            var successEvent = new AdminLoginAttemptedEvent
            {
                AdminId = State.Id,
                Username = command.Username,
                Success = true,
                FailureReason = null,
                IpAddress = command.IpAddress,
                UserAgent = command.UserAgent,
                AttemptedAt = DateTime.UtcNow,
                RequiredTwoFactor = State.RequiresTwoFactor(),
                CommandId = command.CommandId
            };

            RaiseEvent(successEvent);
            await ConfirmEvents();

            return new LoginResult
            {
                Success = true,
                Message = "Login successful",
                Admin = State.ToDto(),
                // Token生成将在服务层处理
                ExpiresAt = DateTime.UtcNow.AddHours(8)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during admin login: {Username}", command.Username);
            return new LoginResult
            {
                Success = false,
                Message = "Login failed due to internal error"
            };
        }
    }

    public async Task<TwoFactorSetupResult> EnableTwoFactorAsync(EnableTwoFactorCommand command)
    {
        try
        {
            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new TwoFactorSetupResult
                {
                    Success = false,
                    Message = "Admin not found"
                };
            }

            // 生成新的密钥
            var secretKey = _totpService.GenerateSecret();

            // 验证提供的验证码
            var isValidCode = _totpService.VerifyCode(command.VerificationCode, secretKey);
            if (!isValidCode)
            {
                return new TwoFactorSetupResult
                {
                    Success = false,
                    Message = "Invalid verification code"
                };
            }

            // 生成恢复代码
            var recoveryCodes = _totpService.GenerateRecoveryCodes();

            // 创建2FA启用事件
            var twoFactorEnabledEvent = new TwoFactorEnabledEvent
            {
                AdminId = State.Id,
                SecretKey = secretKey,
                EnabledAt = DateTime.UtcNow,
                RecoveryCodes = recoveryCodes,
                CommandId = command.CommandId
            };

            RaiseEvent(twoFactorEnabledEvent);
            await ConfirmEvents();

            return new TwoFactorSetupResult
            {
                Success = true,
                Message = "Two-factor authentication enabled successfully",
                SecretKey = secretKey,
                QrCodeUrl = _totpService.GenerateQrCodeUrl(State.Username, secretKey),
                RecoveryCodes = recoveryCodes
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling 2FA for admin: {AdminId}", command.AdminId);
            return new TwoFactorSetupResult
            {
                Success = false,
                Message = "Failed to enable two-factor authentication"
            };
        }
    }

    public async Task<AdminOperationResult> VerifyTwoFactorAsync(VerifyTwoFactorCommand command)
    {
        try
        {
            // 验证2FA代码
            var isValid = await VerifyTwoFactorCode(command.Code);

            var verificationEvent = new TwoFactorCodeVerifiedEvent
            {
                AdminId = State.Id,
                Success = isValid,
                VerifiedAt = DateTime.UtcNow,
                IpAddress = string.Empty, // 需要从上下文获取
                FailureReason = isValid ? null : "Invalid code",
                CommandId = command.CommandId
            };

            RaiseEvent(verificationEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = isValid,
                Message = isValid ? "Two-factor code verified" : "Invalid two-factor code"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying 2FA code for admin: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to verify two-factor code",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> AssignRoleAsync(AssignRoleCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin not found",
                    ErrorCode = "ADMIN_NOT_FOUND"
                };
            }

            // 检查是否可以分配角色
            if (!State.CanAssignRole())
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Cannot assign role in current state",
                    ErrorCode = "INVALID_STATE"
                };
            }

            // 检查是否已有该角色
            if (State.RoleIds.Contains(command.RoleId))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Role already assigned",
                    ErrorCode = "ROLE_ALREADY_ASSIGNED"
                };
            }

            // 创建角色分配事件
            var roleAssignedEvent = new AdminRoleAssignedEvent
            {
                AdminId = State.Id,
                RoleId = command.RoleId,
                RoleName = string.Empty, // 将由事件处理器填充
                AssignedAt = DateTime.UtcNow,
                AssignedBy = command.AssignedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(roleAssignedEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Role assigned successfully",
                Admin = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role to admin: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to assign role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> RemoveRoleAsync(RemoveRoleCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin not found",
                    ErrorCode = "ADMIN_NOT_FOUND"
                };
            }

            // 检查是否有该角色
            if (!State.RoleIds.Contains(command.RoleId))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Role not assigned",
                    ErrorCode = "ROLE_NOT_ASSIGNED"
                };
            }

            // 创建角色移除事件
            var roleRemovedEvent = new AdminRoleRemovedEvent
            {
                AdminId = State.Id,
                RoleId = command.RoleId,
                RoleName = string.Empty, // 将由事件处理器填充
                RemovedAt = DateTime.UtcNow,
                RemovedBy = command.RemovedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(roleRemovedEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Role removed successfully",
                Admin = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role from admin: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to remove role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> UpdateStatusAsync(UpdateAdminStatusCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin not found",
                    ErrorCode = "ADMIN_NOT_FOUND"
                };
            }

            // 创建状态更新事件
            var statusChangedEvent = new AdminStatusChangedEvent
            {
                AdminId = State.Id,
                OldStatus = State.Status,
                NewStatus = command.NewStatus,
                Reason = command.Reason,
                ChangedAt = DateTime.UtcNow,
                ChangedBy = command.UpdatedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(statusChangedEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Admin status updated successfully",
                Admin = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating admin status: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to update admin status",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public Task<PermissionCheckResult> HasPermissionAsync(CheckPermissionCommand command)
    {
        // 这里需要调用权限查询Grain来检查权限
        // 临时实现，实际需要PermissionQueryGrain
        return Task.FromResult(new PermissionCheckResult
        {
            HasPermission = false, // 默认无权限
            AdminId = command.AdminId,
            Resource = command.Resource,
            Action = command.Action
        });
    }

    public async Task<List<PermissionDto>> GetPermissionsAsync()
    {
        // 这里需要调用权限查询Grain来获取权限
        // 临时实现，返回空列表
        await Task.CompletedTask;
        return new List<PermissionDto>();
    }

    public async Task<List<RoleDto>> GetRolesAsync()
    {
        // 这里需要调用权限查询Grain来获取角色
        // 临时实现，返回空列表
        await Task.CompletedTask;
        return new List<RoleDto>();
    }

    public async Task<AdminOperationResult> ResetPasswordAsync(string newPassword, string operatorId)
    {
        try
        {
            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin not found",
                    ErrorCode = "ADMIN_NOT_FOUND"
                };
            }

            // 创建密码重置事件
            var passwordHash = HashPassword(newPassword);
            var passwordChangedEvent = new AdminPasswordChangedEvent
            {
                AdminId = State.Id,
                PasswordHash = passwordHash,
                ChangedAt = DateTime.UtcNow,
                IsInitialSetup = false,
                CommandId = Guid.NewGuid().ToString()
            };

            RaiseEvent(passwordChangedEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Password reset successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password for admin: {AdminId}", State.Id);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to reset password",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> UnlockAccountAsync(string operatorId)
    {
        try
        {
            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin not found",
                    ErrorCode = "ADMIN_NOT_FOUND"
                };
            }

            // 创建状态更新事件
            var statusChangedEvent = new AdminStatusChangedEvent
            {
                AdminId = State.Id,
                OldStatus = State.Status,
                NewStatus = AdminStatus.Active,
                Reason = "Account unlocked by administrator",
                ChangedAt = DateTime.UtcNow,
                ChangedBy = operatorId,
                CommandId = Guid.NewGuid().ToString()
            };

            RaiseEvent(statusChangedEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Account unlocked successfully",
                Admin = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unlocking admin account: {AdminId}", State.Id);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to unlock account",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    // 恢复代码相关方法
    public async Task<AdminOperationResult> DisableTwoFactorAsync(DisableTwoFactorCommand command)
    {
        try
        {
            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Admin not found",
                    ErrorCode = "ADMIN_NOT_FOUND"
                };
            }

            // 检查是否启用了2FA
            if (!State.TwoFactorEnabled)
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Two-factor authentication is not enabled",
                    ErrorCode = "2FA_NOT_ENABLED"
                };
            }

            // 验证当前密码
            if (!VerifyPassword(command.CurrentPassword, State.PasswordHash))
            {
                return new AdminOperationResult
                {
                    Success = false,
                    Message = "Current password is incorrect",
                    ErrorCode = "INVALID_PASSWORD"
                };
            }

            // 创建2FA禁用事件
            var twoFactorDisabledEvent = new TwoFactorDisabledEvent
            {
                AdminId = State.Id,
                DisabledAt = DateTime.UtcNow,
                DisabledBy = command.DisabledBy,
                Reason = command.Reason,
                CommandId = command.CommandId
            };

            RaiseEvent(twoFactorDisabledEvent);
            await ConfirmEvents();

            return new AdminOperationResult
            {
                Success = true,
                Message = "Two-factor authentication disabled successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling 2FA for admin: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to disable two-factor authentication",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RecoveryCodesResult> RegenerateRecoveryCodesAsync(RegenerateRecoveryCodesCommand command)
    {
        try
        {
            // 检查管理员是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new RecoveryCodesResult
                {
                    Success = false,
                    Message = "Admin not found"
                };
            }

            // 检查是否启用了2FA
            if (!State.TwoFactorEnabled)
            {
                return new RecoveryCodesResult
                {
                    Success = false,
                    Message = "Two-factor authentication is not enabled"
                };
            }

            // 验证当前密码
            if (!VerifyPassword(command.CurrentPassword, State.PasswordHash))
            {
                return new RecoveryCodesResult
                {
                    Success = false,
                    Message = "Current password is incorrect"
                };
            }

            // 生成新的恢复代码
            var newRecoveryCodes = _totpService.GenerateRecoveryCodes();

            // 创建恢复代码重新生成事件（实际上是禁用旧的再启用新的）
            var twoFactorEnabledEvent = new TwoFactorEnabledEvent
            {
                AdminId = State.Id,
                SecretKey = State.TwoFactorSecret!, // 保持原有密钥
                EnabledAt = DateTime.UtcNow,
                RecoveryCodes = newRecoveryCodes,
                CommandId = command.CommandId
            };

            RaiseEvent(twoFactorEnabledEvent);
            await ConfirmEvents();

            return new RecoveryCodesResult
            {
                Success = true,
                Message = "Recovery codes regenerated successfully",
                RecoveryCodes = newRecoveryCodes,
                RemainingCodes = newRecoveryCodes.Count
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating recovery codes for admin: {AdminId}", command.AdminId);
            return new RecoveryCodesResult
            {
                Success = false,
                Message = "Failed to regenerate recovery codes"
            };
        }
    }

    public Task<int> GetRemainingRecoveryCodesCountAsync()
    {
        return Task.FromResult(State.RecoveryCodes.Count);
    }

    // 私有辅助方法
    private static string HashPassword(string password)
    {
        // 使用BCrypt进行密码哈希
        return BCrypt.Net.BCrypt.HashPassword(password, 12);
    }

    private static bool VerifyPassword(string password, string hash)
    {
        try
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> VerifyTwoFactorCode(string code)
    {
        if (string.IsNullOrEmpty(State.TwoFactorSecret))
            return false;

        // 首先尝试TOTP验证
        if (_totpService.VerifyCode(code, State.TwoFactorSecret))
            return true;

        // 如果是恢复代码，验证并使用
        if (_totpService.VerifyRecoveryCode(code, State.RecoveryCodes))
        {
            // 记录恢复代码使用事件
            var recoveryCodeUsedEvent = new RecoveryCodeUsedEvent
            {
                AdminId = State.Id,
                RecoveryCode = code,
                UsedAt = DateTime.UtcNow,
                IpAddress = string.Empty, // 需要从上下文获取
                CommandId = Guid.NewGuid().ToString()
            };

            RaiseEvent(recoveryCodeUsedEvent);
            await ConfirmEvents();

            return true;
        }

        return false;
    }
}