using Orleans;
using Orleans.Runtime;
using Microsoft.Extensions.Logging;
using Curio.Orleans.Interfaces.Users;

namespace Curio.Orleans.Grains.Users;

/// <summary>
/// 邮箱索引状态
/// </summary>
[GenerateSerializer]
public class EmailIndexState
{
    [Id(0)] public string? UserId { get; set; }

    [Id(1)] public DateTime CreatedAt { get; set; }

    [Id(2)] public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// 邮箱索引 Grain 实现
/// 维护邮箱地址到用户 UUID 的映射关系
/// </summary>
public class EmailIndexGrain : Grain, IEmailIndexGrain
{
    private readonly ILogger<EmailIndexGrain> _logger;
    private readonly IPersistentState<EmailIndexState> _state;

    public EmailIndexGrain(
        ILogger<EmailIndexGrain> logger,
        [PersistentState("emailIndex", "Default")] IPersistentState<EmailIndexState> state)
    {
        _logger = logger;
        _state = state;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);
        _logger.LogDebug("EmailIndexGrain activated: {Email}", this.GetPrimaryKeyString());
    }

    public async Task SetUserIdAsync(string userId)
    {
        if (string.IsNullOrEmpty(userId))
        {
            throw new ArgumentException("UserId cannot be null or empty", nameof(userId));
        }

        _state.State.UserId = userId;
        _state.State.UpdatedAt = DateTime.UtcNow;

        if (_state.State.CreatedAt == default)
        {
            _state.State.CreatedAt = DateTime.UtcNow;
        }

        await _state.WriteStateAsync();

        _logger.LogInformation("Email mapped to user: {Email} -> {UserId}",
            this.GetPrimaryKeyString(), userId);
    }

    public Task<string?> GetUserIdAsync()
    {
        return Task.FromResult(_state.State.UserId);
    }

    public async Task RemoveAsync()
    {
        var email = this.GetPrimaryKeyString();

        _state.State.UserId = null;
        _state.State.UpdatedAt = DateTime.UtcNow;

        await _state.WriteStateAsync();

        _logger.LogInformation("Email mapping removed: {Email}", email);
    }

    public Task<bool> ExistsAsync()
    {
        return Task.FromResult(!string.IsNullOrEmpty(_state.State.UserId));
    }
}
