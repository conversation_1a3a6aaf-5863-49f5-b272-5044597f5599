using Orleans;
using Orleans.Streams;
using Microsoft.Extensions.Logging;
using Curio.Orleans.Interfaces.Users;
using Curio.Shared.Users;
using Curio.Orleans.Grains.Base;
using Curio.Events.State;
using Curio.Events.Commands;

namespace Curio.Orleans.Grains.Users;

/// <summary>
/// 用户聚合 - 简化版本，专注于Orleans事件溯源能力
/// 移除DDD复杂性，保留核心的业务逻辑和事件处理
/// 使用领域专用命令对象，与Web层解耦
/// </summary>
public class UserGrain : ResilientJournaledGrain<UserState, DomainEvent>, IUserGrain
{
    private IAsyncStream<DomainEvent>? _eventStream;

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);

        // 设置事件流用于发布到外部系统
        var streamProvider = this.GetStreamProvider("KafkaStreams");
        var streamId = SanitizeStreamIdentifier($"user-events-{this.GetPrimaryKeyString()}");
        _eventStream = streamProvider.GetStream<DomainEvent>("user-events", streamId);

        Logger.LogInformation("UserGrain activated: {UserId}", this.GetPrimaryKeyString());
    }

    public Task<EmailExistsResult> CheckUserExistsAsync(CheckUserExistsDomainCommand command)
    {
        // 幂等性检查
        if (State.ProcessedCommands.Contains(command.CommandId))
        {
            return Task.FromResult(CreateEmailExistsResult());
        }

        // 记录命令处理
        State.ProcessedCommands.Add(command.CommandId);
        return Task.FromResult(CreateEmailExistsResult());
    }

    public async Task<VerificationResult> RegisterUserAsync(RegisterUserDomainCommand command)
    {
        // 幂等性检查
        if (State.ProcessedCommands.Contains(command.CommandId))
        {
            return CreateResult(false, "Command already processed");
        }

        // 简化的业务规则验证
        if (!State.CanRegister(command.Email, command.Name))
        {
            return CreateResult(false, "Cannot register user with provided information");
        }

        // 创建并应用事件（注意：验证码验证已在命令处理器中完成）
        var registrationEvent = new UserRegisteredEvent
        {
            UserId = this.GetPrimaryKeyString(),
            Email = command.Email,
            Name = command.Name,
            RegisteredAt = DateTime.UtcNow,
            CommandId = command.CommandId,
            RequestSource = command.RequestSource
        };

        // Orleans 事件溯源：应用事件到状态
        RaiseEvent(registrationEvent);
        await ConfirmEvents();

        // 发布事件到外部系统
        await PublishEventAsync(registrationEvent);

        return CreateResult(true, "User registered successfully", State.ToDto());
    }

    public async Task<VerificationResult> LoginUserAsync(LoginUserDomainCommand command)
    {
        // 幂等性检查
        if (State.ProcessedCommands.Contains(command.CommandId))
        {
            return CreateResult(false, "Command already processed");
        }

        // 检查用户状态
        if (!CanLogin())
        {
            await PublishLoginFailedEventAsync(command, "User not found or not verified");
            return CreateResult(false, "User not found or not verified");
        }

        // 创建登录成功事件（注意：验证码验证已在命令处理器中完成）
        var loginEvent = new UserLoginAttemptedEvent
        {
            Email = command.Email,
            Success = true,
            FailureReason = null,
            AttemptedAt = DateTime.UtcNow,
            CommandId = command.CommandId
        };

        // 应用事件
        RaiseEvent(loginEvent);
        await ConfirmEvents();
        await PublishEventAsync(loginEvent);

        return CreateResult(true, "Login successful", State.ToDto());
    }

    public Task<UserDto?> GetUserAsync()
    {
        return Task.FromResult(string.IsNullOrEmpty(State.Email) ? null : State.ToDto());
    }

    // 简化的辅助方法
    private EmailExistsResult CreateEmailExistsResult() => new()
    {
        Exists = !string.IsNullOrEmpty(State.Email),
        IsVerified = State.IsVerified
    };

    private static VerificationResult CreateResult(bool success, string message, UserDto? user = null) => new()
    {
        Success = success,
        Message = message,
        User = user
    };

    private bool CanLogin() => !string.IsNullOrEmpty(State.Email) && State.IsVerified;

    private async Task<bool> ValidateVerificationCodeAsync(string email, string code, string purpose)
    {
        var verificationGrain = GrainFactory.GetGrain<IVerificationGrain>(email);
        return await verificationGrain.VerifyCodeAsync(code, purpose);
    }

    private async Task PublishEventAsync(DomainEvent domainEvent)
    {
        if (_eventStream != null)
        {
            await _eventStream.OnNextAsync(domainEvent);
        }
    }

    private async Task PublishLoginFailedEventAsync(LoginUserDomainCommand command, string reason)
    {
        var loginFailedEvent = new UserLoginAttemptedEvent
        {
            Email = command.Email,
            Success = false,
            FailureReason = reason,
            AttemptedAt = DateTime.UtcNow,
            CommandId = command.CommandId
        };

        RaiseEvent(loginFailedEvent);
        await ConfirmEvents();
        await PublishEventAsync(loginFailedEvent);
    }

    private async Task PublishLoginFailedEventAsync(LoginUserCommand command, string reason)
    {
        var loginFailedEvent = new UserLoginAttemptedEvent
        {
            Email = command.Email,
            Success = false,
            FailureReason = reason,
            AttemptedAt = DateTime.UtcNow,
            CommandId = command.CommandId
        };

        RaiseEvent(loginFailedEvent);
        await ConfirmEvents();
        await PublishEventAsync(loginFailedEvent);
    }
}