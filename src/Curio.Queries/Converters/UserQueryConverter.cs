using Curio.Shared.Users;
using Curio.Events.Commands;

namespace Curio.Queries.Converters;

/// <summary>
/// 用户查询转换器 - 将Web层查询对象转换为领域层查询对象
/// 保持查询端的分层架构原则
/// </summary>
public static class UserQueryConverter
{
    /// <summary>
    /// 将Web邮箱检查查询转换为领域查询
    /// </summary>
    public static CheckUserExistsDomainCommand ToDomainCommand(this CheckEmailExistsCommand webQuery, string? requestSource = null)
    {
        if (webQuery == null) throw new ArgumentNullException(nameof(webQuery));
        
        return new CheckUserExistsDomainCommand
        {
            Email = webQuery.Email?.Trim().ToLowerInvariant() ?? string.Empty,
            CommandId = !string.IsNullOrEmpty(webQuery.CommandId) ? webQuery.CommandId : Guid.NewGuid().ToString(),
            RequestedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 验证查询命令的有效性
    /// </summary>
    public static void ValidateQuery(this CheckUserExistsDomainCommand query)
    {
        if (string.IsNullOrWhiteSpace(query.Email))
            throw new ArgumentException("Email is required", nameof(query.Email));
            
        if (!IsValidEmail(query.Email))
            throw new ArgumentException("Invalid email format", nameof(query.Email));
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}