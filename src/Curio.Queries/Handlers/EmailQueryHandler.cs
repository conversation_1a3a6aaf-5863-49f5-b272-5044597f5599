using Orleans;
using Curio.Orleans.Interfaces.Email;

namespace Curio.Queries.Handlers;

/// <summary>
/// 邮件状态查询处理器 - 简化的CQRS查询端
/// 直接查询EmailConsumerGrain，移除中间层抽象
/// </summary>
public class EmailQueryHandler
{
    private readonly IGrainFactory _grainFactory;

    public EmailQueryHandler(IGrainFactory grainFactory)
    {
        _grainFactory = grainFactory;
    }

    /// <summary>
    /// 获取邮件发送状态
    /// </summary>
    public async Task<EmailSendingResult?> HandleGetEmailSendingStatusAsync(string email, string eventId)
    {
        var emailConsumerGrain = _grainFactory.GetGrain<IEmailConsumerGrain>(email);
        return await emailConsumerGrain.GetEmailSendingStatusAsync(eventId);
    }

    /// <summary>
    /// 获取邮件发送统计
    /// </summary>
    public async Task<EmailSendingStats> HandleGetEmailSendingStatsAsync(string email)
    {
        var emailConsumerGrain = _grainFactory.GetGrain<IEmailConsumerGrain>(email);
        return await emailConsumerGrain.GetSendingStatsAsync();
    }
}