using Curio.Shared.Users;

namespace Curio.Events.State;

/// <summary>
/// 用户状态 - 简化版本，专注于事件溯源
/// 移除复杂的DDD概念，保留核心的状态管理
/// </summary>
public class UserState
{
    public string Id { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public UserStatus Status { get; private set; } = UserStatus.PendingVerification;
    public DateTime RegisteredAt { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public HashSet<string> ProcessedCommands { get; private set; } = new();

    /// <summary>
    /// 简化的业务规则验证
    /// </summary>
    public bool CanRegister(string email, string name)
    {
        return !string.IsNullOrWhiteSpace(email) && 
               !string.IsNullOrWhiteSpace(name) && 
               IsValidEmail(email) &&
               string.IsNullOrEmpty(Id); // 用户未注册
    }
    
    public bool IsVerified => Status == UserStatus.Active && VerifiedAt.HasValue;
    
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 事件应用方法 - Orleans JournaledGrain 自动调用
    /// </summary>
    public void ApplyEvent(UserRegisteredEvent @event)
    {
        Id = @event.UserId;
        Email = @event.Email;
        Name = @event.Name;
        Status = UserStatus.Active;
        RegisteredAt = @event.RegisteredAt;
        VerifiedAt = @event.RegisteredAt;
        ProcessedCommands.Add(@event.CommandId);
    }

    public void ApplyEvent(UserLoginAttemptedEvent @event)
    {
        ProcessedCommands.Add(@event.CommandId);
    }

    /// <summary>
    /// 转换为DTO
    /// </summary>
    public UserDto ToDto()
    {
        return new UserDto
        {
            Id = Id,
            Email = Email,
            Name = Name,
            RegisteredAt = RegisteredAt,
            IsVerified = IsVerified
        };
    }
}