using Orleans;

namespace Curio.Events.Commands;

/// <summary>
/// 领域层专用的用户命令 - 与Web层请求对象分离
/// </summary>

[GenerateSerializer]
public class RegisterUserDomainCommand
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string Name { get; set; } = string.Empty;
    [Id(2)] public string CommandId { get; set; } = string.Empty;
    [Id(3)] public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
    [Id(4)] public string? RequestSource { get; set; } // 可选的请求来源标识
}

[GenerateSerializer]
public class LoginUserDomainCommand
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string CommandId { get; set; } = string.Empty;
    [Id(2)] public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
    [Id(3)] public string? RequestSource { get; set; }
}

[GenerateSerializer]
public class CheckUserExistsDomainCommand
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string CommandId { get; set; } = string.Empty;
    [Id(2)] public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
}