using Orleans;
using Curio.Shared.Users;
using Curio.Events.Commands;

namespace Curio.Orleans.Interfaces.Users;

/// <summary>
/// 用户聚合Grain接口 - 使用领域专用命令对象
/// 分离Web层请求对象和领域层命令对象
/// </summary>
public interface IUserGrain : IGrainWithStringKey
{
    /// <summary>
    /// 检查用户是否存在（领域查询）
    /// </summary>
    Task<EmailExistsResult> CheckUserExistsAsync(CheckUserExistsDomainCommand command);

    /// <summary>
    /// 注册用户（需要事先验证验证码）
    /// </summary>
    Task<VerificationResult> RegisterUserAsync(RegisterUserDomainCommand command);

    /// <summary>
    /// 用户登录（需要事先验证验证码）
    /// </summary>
    Task<VerificationResult> LoginUserAsync(LoginUserDomainCommand command);

    /// <summary>
    /// 获取用户信息
    /// </summary>
    Task<UserDto?> GetUserAsync();
}