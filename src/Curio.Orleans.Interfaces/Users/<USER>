using Orleans;
using Curio.Shared.Users;

namespace Curio.Orleans.Interfaces.Users;

/// <summary>
/// 用户管理 Grain 接口
/// 协调 UserGrain 和 EmailIndexGrain 的操作
/// </summary>
public interface IUserManagerGrain : IGrainWithStringKey
{
    /// <summary>
    /// 通过邮箱查找用户
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>用户信息，如果不存在返回 null</returns>
    Task<UserDto?> FindUserByEmailAsync(string email);

    /// <summary>
    /// 检查邮箱是否已存在
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>是否存在</returns>
    Task<bool> CheckEmailExistsAsync(string email);

    /// <summary>
    /// 注册新用户
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="password">密码</param>
    /// <returns>注册结果</returns>
    Task<VerificationResult> RegisterUserAsync(string email, string password);

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="password">密码</param>
    /// <returns>登录结果</returns>
    Task<VerificationResult> LoginUserAsync(string email, string password);

    /// <summary>
    /// 更新用户邮箱
    /// </summary>
    /// <param name="userId">用户 UUID</param>
    /// <param name="newEmail">新邮箱</param>
    /// <param name="oldEmail">旧邮箱</param>
    /// <returns>更新结果</returns>
    Task<VerificationResult> UpdateUserEmailAsync(string userId, string newEmail, string oldEmail);
}
