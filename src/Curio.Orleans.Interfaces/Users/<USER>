using Orleans;

namespace Curio.Orleans.Interfaces.Users;

/// <summary>
/// 邮箱索引 Grain 接口
/// 用于通过邮箱地址查找用户 UUID
/// </summary>
public interface IEmailIndexGrain : IGrainWithStringKey
{
    /// <summary>
    /// 设置邮箱对应的用户 UUID
    /// </summary>
    /// <param name="userId">用户 UUID</param>
    Task SetUserIdAsync(string userId);
    
    /// <summary>
    /// 获取邮箱对应的用户 UUID
    /// </summary>
    /// <returns>用户 UUID，如果不存在返回 null</returns>
    Task<string?> GetUserIdAsync();
    
    /// <summary>
    /// 删除邮箱索引
    /// </summary>
    Task RemoveAsync();
    
    /// <summary>
    /// 检查邮箱是否已被使用
    /// </summary>
    Task<bool> ExistsAsync();
}
