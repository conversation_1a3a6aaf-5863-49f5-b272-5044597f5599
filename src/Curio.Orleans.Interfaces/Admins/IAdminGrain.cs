using Orleans;
using Curio.Shared.Admins;

namespace Curio.Orleans.Interfaces.Admins;

public interface IAdminGrain : IGrainWithStringKey
{
    /// <summary>
    /// 获取管理员信息
    /// </summary>
    Task<AdminDto?> GetAdminAsync();
    
    /// <summary>
    /// 创建管理员
    /// </summary>
    Task<AdminOperationResult> CreateAdminAsync(CreateAdminCommand command);
    
    /// <summary>
    /// 更新管理员信息
    /// </summary>
    Task<AdminOperationResult> UpdateAdminAsync(UpdateAdminCommand command);
    
    /// <summary>
    /// 修改管理员密码
    /// </summary>
    Task<AdminOperationResult> ChangePasswordAsync(ChangeAdminPasswordCommand command);
    
    /// <summary>
    /// 管理员登录
    /// </summary>
    Task<LoginResult> LoginAsync(AdminLoginCommand command);
    
    /// <summary>
    /// 启用两步验证
    /// </summary>
    Task<TwoFactorSetupResult> EnableTwoFactorAsync(EnableTwoFactorCommand command);
    
    /// <summary>
    /// 禁用两步验证
    /// </summary>
    Task<AdminOperationResult> DisableTwoFactorAsync(DisableTwoFactorCommand command);
    
    /// <summary>
    /// 验证两步验证码
    /// </summary>
    Task<AdminOperationResult> VerifyTwoFactorAsync(VerifyTwoFactorCommand command);
    
    /// <summary>
    /// 重新生成恢复代码
    /// </summary>
    Task<RecoveryCodesResult> RegenerateRecoveryCodesAsync(RegenerateRecoveryCodesCommand command);
    
    /// <summary>
    /// 获取剩余恢复代码数量
    /// </summary>
    Task<int> GetRemainingRecoveryCodesCountAsync();
    
    /// <summary>
    /// 分配角色
    /// </summary>
    Task<AdminOperationResult> AssignRoleAsync(AssignRoleCommand command);
    
    /// <summary>
    /// 移除角色
    /// </summary>
    Task<AdminOperationResult> RemoveRoleAsync(RemoveRoleCommand command);
    
    /// <summary>
    /// 更新管理员状态
    /// </summary>
    Task<AdminOperationResult> UpdateStatusAsync(UpdateAdminStatusCommand command);
    
    /// <summary>
    /// 检查管理员是否具有指定权限
    /// </summary>
    Task<PermissionCheckResult> HasPermissionAsync(CheckPermissionCommand command);
    
    /// <summary>
    /// 获取管理员的所有权限
    /// </summary>
    Task<List<PermissionDto>> GetPermissionsAsync();
    
    /// <summary>
    /// 获取管理员的所有角色
    /// </summary>
    Task<List<RoleDto>> GetRolesAsync();
    
    /// <summary>
    /// 重置管理员密码（管理员操作）
    /// </summary>
    Task<AdminOperationResult> ResetPasswordAsync(string newPassword, string operatorId);
    
    /// <summary>
    /// 解锁管理员账户
    /// </summary>
    Task<AdminOperationResult> UnlockAccountAsync(string operatorId);
}