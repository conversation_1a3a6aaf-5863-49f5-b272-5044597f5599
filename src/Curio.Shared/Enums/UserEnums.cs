namespace Curio.Shared.Users;

public enum UserStatus
{
    PendingVerification,
    Active,
    Suspended,
    Deleted
}

public enum VerificationPurpose
{
    Registration,
    Login,
    PasswordReset
}

// 管理员状态枚举
public enum AdminStatus
{
    Active,           // 活跃
    Disabled,         // 禁用
    Locked,           // 锁定
    PendingSetup,     // 待设置
    Suspended         // 暂停
}

// 权限操作枚举
public enum PermissionAction
{
    Read,
    Create,
    Update,
    Delete,
    Execute,
    Manage
}

// 权限资源枚举
public enum PermissionResource
{
    Users,           // 用户管理
    Admins,          // 管理员管理
    Roles,           // 角色管理
    Permissions,     // 权限管理
    SystemConfig,    // 系统配置
    AuditLogs,       // 审计日志
    EmailTemplates,  // 邮件模板
    Monitoring,      // 系统监控
    ProjectionManagement, // 投影管理
    DeadLetterQueue  // 死信队列管理
}

// 审计日志等级枚举
public enum AuditLevel
{
    Info,
    Warning,
    Error,
    Critical
}

// 审计操作类型枚举
public enum AuditActionType
{
    Login,
    Logout,
    Create,
    Update,
    Delete,
    View,
    Execute,
    SystemConfig,
    PermissionChange,
    RoleAssignment,
    PasswordChange,
    TwoFactorSetup,
    AccountLock,
    AccountUnlock
}