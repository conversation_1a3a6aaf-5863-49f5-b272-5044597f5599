using Orleans;
using Curio.Shared.Users;

namespace Curio.Shared.Admins;

// === Commands ===

[GenerateSerializer]
public class CreateAdminCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public string Email { get; set; } = string.Empty;
    [Id(3)] public string Name { get; set; } = string.Empty;
    [Id(4)] public string InitialPassword { get; set; } = string.Empty;
    [Id(5)] public List<string> RoleIds { get; set; } = new();
    [Id(6)] public string CreatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class UpdateAdminCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string Email { get; set; } = string.Empty;
    [Id(3)] public string Name { get; set; } = string.Empty;
    [Id(4)] public string UpdatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class ChangeAdminPasswordCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string CurrentPassword { get; set; } = string.Empty;
    [Id(3)] public string NewPassword { get; set; } = string.Empty;
    [Id(4)] public bool IsInitialSetup { get; set; } = false;
}

[GenerateSerializer]
public class AdminLoginCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public string Password { get; set; } = string.Empty;
    [Id(3)] public string? TwoFactorCode { get; set; }
    [Id(4)] public string IpAddress { get; set; } = string.Empty;
    [Id(5)] public string UserAgent { get; set; } = string.Empty;
}

[GenerateSerializer]
public class EnableTwoFactorCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string VerificationCode { get; set; } = string.Empty;
}

[GenerateSerializer]
public class VerifyTwoFactorCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string Code { get; set; } = string.Empty;
    [Id(3)] public string LoginSessionId { get; set; } = string.Empty;
}

[GenerateSerializer]
public class DisableTwoFactorCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string CurrentPassword { get; set; } = string.Empty;
    [Id(3)] public string DisabledBy { get; set; } = string.Empty;
    [Id(4)] public string Reason { get; set; } = string.Empty;
}

[GenerateSerializer]
public class RegenerateRecoveryCodesCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string CurrentPassword { get; set; } = string.Empty;
}

[GenerateSerializer]
public class UseRecoveryCodeCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string RecoveryCode { get; set; } = string.Empty;
    [Id(3)] public string IpAddress { get; set; } = string.Empty;
}

[GenerateSerializer]
public class AssignRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string RoleId { get; set; } = string.Empty;
    [Id(3)] public string AssignedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class RemoveRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string RoleId { get; set; } = string.Empty;
    [Id(3)] public string RemovedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class UpdateAdminStatusCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public AdminStatus NewStatus { get; set; }
    [Id(3)] public string Reason { get; set; } = string.Empty;
    [Id(4)] public string UpdatedBy { get; set; } = string.Empty;
}

// === Role Commands ===

[GenerateSerializer]
public class CreateRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleName { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public List<PermissionAssignment> Permissions { get; set; } = new();
    [Id(4)] public string CreatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class UpdateRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public string RoleName { get; set; } = string.Empty;
    [Id(3)] public string Description { get; set; } = string.Empty;
    [Id(4)] public string UpdatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class DeleteRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public string DeletedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class AssignPermissionCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public PermissionResource Resource { get; set; }
    [Id(3)] public PermissionAction Action { get; set; }
    [Id(4)] public string AssignedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class RemovePermissionCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public PermissionResource Resource { get; set; }
    [Id(3)] public PermissionAction Action { get; set; }
    [Id(4)] public string RemovedBy { get; set; } = string.Empty;
}

// === Query Commands ===

[GenerateSerializer]
public class CheckPermissionCommand
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public PermissionResource Resource { get; set; }
    [Id(2)] public PermissionAction Action { get; set; }
}

[GenerateSerializer]
public class GetAdminPermissionsCommand
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
}

// === Helper Classes ===

[GenerateSerializer]
public class PermissionAssignment
{
    [Id(0)] public PermissionResource Resource { get; set; }
    [Id(1)] public PermissionAction Action { get; set; }
}

// === Results ===

[GenerateSerializer]
public class AdminOperationResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public AdminDto? Admin { get; set; }
    [Id(3)] public string? ErrorCode { get; set; }
}

[GenerateSerializer]
public class LoginResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public AdminDto? Admin { get; set; }
    [Id(3)] public string? AccessToken { get; set; }
    [Id(4)] public string? RefreshToken { get; set; }
    [Id(5)] public bool RequiresTwoFactor { get; set; }
    [Id(6)] public string? TwoFactorSessionId { get; set; }
    [Id(7)] public DateTime? ExpiresAt { get; set; }
}

[GenerateSerializer]
public class TwoFactorSetupResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public string? SecretKey { get; set; }
    [Id(3)] public string? QrCodeUrl { get; set; }
    [Id(4)] public List<string> RecoveryCodes { get; set; } = new();
}

[GenerateSerializer]
public class RecoveryCodesResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public List<string> RecoveryCodes { get; set; } = new();
    [Id(3)] public int RemainingCodes { get; set; }
}

[GenerateSerializer]
public class RoleOperationResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public RoleDto? Role { get; set; }
    [Id(3)] public string? ErrorCode { get; set; }
}

[GenerateSerializer]
public class PermissionCheckResult
{
    [Id(0)] public bool HasPermission { get; set; }
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public PermissionResource Resource { get; set; }
    [Id(3)] public PermissionAction Action { get; set; }
    [Id(4)] public List<string> GrantingRoles { get; set; } = new();
}