<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Curio.Orleans.Interfaces\Curio.Orleans.Interfaces.csproj" />
    <ProjectReference Include="..\Curio.Shared\Curio.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Handlebars.Net" Version="2.1.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Orleans.Core.Abstractions" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Streaming" Version="9.2.1" />
    <PackageReference Include="Confluent.Kafka" Version="2.6.1" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.3" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>