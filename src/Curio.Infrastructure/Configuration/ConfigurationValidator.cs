using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace Curio.Infrastructure.Configuration;

/// <summary>
/// 配置验证器，用于验证应用程序配置的完整性和有效性
/// </summary>
public class ConfigurationValidator
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationValidator> _logger;

    public ConfigurationValidator(IConfiguration configuration, ILogger<ConfigurationValidator> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// 验证所有配置
    /// </summary>
    public ValidationResult ValidateConfiguration()
    {
        var errors = new List<string>();

        // 验证数据库配置
        ValidateDatabaseConfiguration(errors);

        // 验证Orleans配置
        ValidateOrleansConfiguration(errors);

        // 验证Kafka配置
        ValidateKafkaConfiguration(errors);

        // 验证安全配置
        ValidateSecurityConfiguration(errors);

        // 验证邮件配置（可选）
        ValidateEmailConfiguration(errors);

        if (errors.Any())
        {
            var errorMessage = string.Join(Environment.NewLine, errors);
            _logger.LogError("Configuration validation failed:{NewLine}{Errors}", Environment.NewLine, errorMessage);
            return new ValidationResult(errorMessage);
        }

        _logger.LogInformation("Configuration validation passed successfully");
        return ValidationResult.Success!;
    }

    private void ValidateDatabaseConfiguration(List<string> errors)
    {
        var dbSettings = _configuration.GetSection(DatabaseSettings.SectionName).Get<DatabaseSettings>();

        if (dbSettings == null)
        {
            errors.Add("Database configuration section is missing");
            return;
        }

        // Check for database password (required, should come from environment variable)
        var password = _configuration["Database:Password"] ?? _configuration["DATABASE__PASSWORD"];
        if (string.IsNullOrEmpty(password))
        {
            errors.Add("Database password is required. Set DATABASE__PASSWORD environment variable.");
        }

        // Validate connection string or individual components
        var connectionString = dbSettings.GetConnectionString();
        if (string.IsNullOrEmpty(connectionString))
        {
            // If no connection string, validate individual components
            if (string.IsNullOrEmpty(dbSettings.Host))
            {
                errors.Add("Database host is required");
            }

            if (string.IsNullOrEmpty(dbSettings.Database))
            {
                errors.Add("Database name is required");
            }

            if (string.IsNullOrEmpty(dbSettings.Username))
            {
                errors.Add("Database username is required");
            }
        }

        if (dbSettings.Port <= 0 || dbSettings.Port > 65535)
        {
            errors.Add("Database port must be between 1 and 65535");
        }

        if (dbSettings.CommandTimeout <= 0)
        {
            errors.Add("Database command timeout must be greater than 0");
        }
    }

    private void ValidateOrleansConfiguration(List<string> errors)
    {
        var orleansSettings = _configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>();

        if (orleansSettings == null)
        {
            errors.Add("Orleans configuration section is missing");
            return;
        }

        if (string.IsNullOrEmpty(orleansSettings.ClusterId))
        {
            errors.Add("Orleans ClusterId is required");
        }

        if (string.IsNullOrEmpty(orleansSettings.ServiceId))
        {
            errors.Add("Orleans ServiceId is required");
        }

        if (orleansSettings.Clustering.RefreshPeriod <= 0)
        {
            errors.Add("Orleans clustering refresh period must be greater than 0");
        }
    }

    private void ValidateKafkaConfiguration(List<string> errors)
    {
        var kafkaSettings = _configuration.GetSection(KafkaSettings.SectionName).Get<KafkaSettings>();

        if (kafkaSettings == null)
        {
            errors.Add("Kafka configuration section is missing");
            return;
        }

        if (kafkaSettings.BrokerList == null || kafkaSettings.BrokerList.Length == 0)
        {
            errors.Add("Kafka broker list is required");
        }

        if (string.IsNullOrEmpty(kafkaSettings.ConsumerGroupId))
        {
            errors.Add("Kafka consumer group ID is required");
        }

        if (kafkaSettings.SessionTimeoutMs <= 0)
        {
            errors.Add("Kafka session timeout must be greater than 0");
        }
    }

    private void ValidateSecurityConfiguration(List<string> errors)
    {
        var appSettings = _configuration.GetSection(ApplicationSettings.SectionName).Get<ApplicationSettings>();

        if (appSettings?.Security?.Jwt == null)
        {
            errors.Add("JWT security configuration is missing");
            return;
        }

        var jwtSettings = appSettings.Security.Jwt;

        // Check JWT secret key (should come from environment variable)
        var jwtSecretKey = _configuration["Application:Security:Jwt:SecretKey"] ??
                          _configuration["APPLICATION__SECURITY__JWT__SECRETKEY"];

        if (string.IsNullOrEmpty(jwtSecretKey))
        {
            errors.Add("JWT secret key is required. Set APPLICATION__SECURITY__JWT__SECRETKEY environment variable.");
        }
        else if (jwtSecretKey.Length < 32)
        {
            errors.Add("JWT secret key must be at least 32 characters long");
        }

        // Check encryption key and salt (should come from environment variables)
        var encryptionKey = _configuration["Application:Security:Encryption:Key"] ??
                           _configuration["APPLICATION__SECURITY__ENCRYPTION__KEY"];
        var encryptionSalt = _configuration["Application:Security:Encryption:Salt"] ??
                            _configuration["APPLICATION__SECURITY__ENCRYPTION__SALT"];

        if (string.IsNullOrEmpty(encryptionKey))
        {
            errors.Add("Encryption key is required. Set APPLICATION__SECURITY__ENCRYPTION__KEY environment variable.");
        }
        else if (encryptionKey.Length < 32)
        {
            errors.Add("Encryption key must be at least 32 characters long");
        }

        if (string.IsNullOrEmpty(encryptionSalt))
        {
            errors.Add("Encryption salt is required. Set APPLICATION__SECURITY__ENCRYPTION__SALT environment variable.");
        }
        else if (encryptionSalt.Length < 16)
        {
            errors.Add("Encryption salt must be at least 16 characters long");
        }

        if (string.IsNullOrEmpty(jwtSettings.Issuer))
        {
            errors.Add("JWT issuer is required");
        }

        if (string.IsNullOrEmpty(jwtSettings.Audience))
        {
            errors.Add("JWT audience is required");
        }

        if (jwtSettings.ExpirationMinutes <= 0)
        {
            errors.Add("JWT expiration minutes must be greater than 0");
        }
    }

    private void ValidateEmailConfiguration(List<string> errors)
    {
        var emailSettings = _configuration.GetSection(EmailSettings.SectionName).Get<EmailSettings>();

        if (emailSettings?.Smtp == null)
        {
            // Email configuration is optional, just log a warning
            _logger.LogWarning("Email configuration is not provided - email functionality will be disabled");
            return;
        }

        if (string.IsNullOrEmpty(emailSettings.Smtp.Host))
        {
            errors.Add("SMTP host is required when email is configured");
        }

        if (emailSettings.Smtp.Port <= 0 || emailSettings.Smtp.Port > 65535)
        {
            errors.Add("SMTP port must be between 1 and 65535");
        }

        // Check for SMTP credentials (should come from environment variables if configured)
        var smtpUsername = _configuration["Email:Smtp:Username"] ?? _configuration["EMAIL__SMTP__USERNAME"];
        var smtpPassword = _configuration["Email:Smtp:Password"] ?? _configuration["EMAIL__SMTP__PASSWORD"];

        if (!string.IsNullOrEmpty(smtpUsername) && string.IsNullOrEmpty(smtpPassword))
        {
            errors.Add("SMTP password is required when SMTP username is configured. Set EMAIL__SMTP__PASSWORD environment variable.");
        }

        // Check sender email (should come from environment variable if configured)
        var fromEmail = _configuration["Email:DefaultSender:FromEmail"] ?? _configuration["EMAIL__DEFAULTSENDER__FROMEMAIL"];
        if (string.IsNullOrEmpty(fromEmail) && !string.IsNullOrEmpty(smtpUsername))
        {
            errors.Add("Default sender email is required when email is configured. Set EMAIL__DEFAULTSENDER__FROMEMAIL environment variable.");
        }
    }
}

/// <summary>
/// 配置验证扩展方法
/// </summary>
public static class ConfigurationValidationExtensions
{
    /// <summary>
    /// 验证配置并在验证失败时抛出异常
    /// </summary>
    public static IServiceCollection ValidateConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetService<ILogger<ConfigurationValidator>>() ??
                    new LoggerFactory().CreateLogger<ConfigurationValidator>();

        var validator = new ConfigurationValidator(configuration, logger);
        var result = validator.ValidateConfiguration();

        if (result != ValidationResult.Success)
        {
            throw new InvalidOperationException($"Configuration validation failed: {result.ErrorMessage}");
        }

        return services;
    }

    /// <summary>
    /// 为生产环境配置安全的配置加载
    /// </summary>
    public static IConfigurationBuilder AddProductionConfiguration(this IConfigurationBuilder builder, IHostEnvironment environment)
    {
        // 基础配置文件
        builder.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
        builder.AddJsonFile($"appsettings.{environment.EnvironmentName}.json", optional: true, reloadOnChange: true);

        if (environment.IsDevelopment())
        {
            // 开发环境：支持用户机密
            builder.AddUserSecrets(typeof(ConfigurationValidator).Assembly);

            // 尝试加载 .env 文件（如果存在）
            var envFile = Path.Combine(Directory.GetCurrentDirectory(), ".env");
            if (File.Exists(envFile))
            {
                // 注意：AddKeyPerFile 不是标准方法，这里改为加载环境变量
                // 如果需要 .env 文件支持，需要添加 DotNetEnv NuGet 包
                // DotNetEnv.Env.Load(envFile);
            }
        }
        else if (environment.IsProduction())
        {
            // 生产环境：优先使用云服务配置

            // Azure Key Vault (如果配置了)
            var keyVaultUrl = Environment.GetEnvironmentVariable("AZURE_KEY_VAULT_URL");
            if (!string.IsNullOrEmpty(keyVaultUrl))
            {
                // 注意：需要添加 Azure.Extensions.AspNetCore.Configuration.Secrets NuGet 包
                // builder.AddAzureKeyVault(new Uri(keyVaultUrl), new DefaultAzureCredential());
            }

            // AWS Systems Manager (如果配置了)
            var awsParameterPrefix = Environment.GetEnvironmentVariable("AWS_PARAMETER_PREFIX");
            if (!string.IsNullOrEmpty(awsParameterPrefix))
            {
                // 注意：需要添加 AWS.Extensions.Configuration.SystemsManager NuGet 包
                // builder.AddSystemsManager(awsParameterPrefix);
            }
        }

        // 环境变量始终具有最高优先级
        builder.AddEnvironmentVariables();

        return builder;
    }

    /// <summary>
    /// 检查生产环境配置安全性
    /// </summary>
    public static void ValidateProductionSecurity(this IConfiguration configuration, IHostEnvironment environment)
    {
        if (!environment.IsProduction()) return;

        var issues = new List<string>();

        // 检查是否有敏感信息直接写在配置文件中
        CheckForHardcodedSecrets(configuration, issues);

        // 检查必需的环境变量
        CheckRequiredEnvironmentVariables(issues);

        if (issues.Count > 0)
        {
            var message = "Production security validation failed:\n" + string.Join("\n", issues);
            throw new InvalidOperationException(message);
        }
    }

    private static void CheckForHardcodedSecrets(IConfiguration configuration, List<string> issues)
    {
        var sensitiveKeys = new[]
        {
            "Database:Password",
            "Application:Security:Jwt:SecretKey",
            "Application:Security:Encryption:Key",
            "Application:Security:Encryption:Salt",
            "Email:Smtp:Password",
            "Kafka:SaslPassword"
        };

        foreach (var key in sensitiveKeys)
        {
            var value = configuration[key];
            if (!string.IsNullOrEmpty(value))
            {
                issues.Add($"Sensitive configuration '{key}' should not be set in appsettings.json. Use environment variables instead.");
            }
        }
    }

    private static void CheckRequiredEnvironmentVariables(List<string> issues)
    {
        var requiredEnvVars = new[]
        {
            "DATABASE__PASSWORD",
            "APPLICATION__SECURITY__JWT__SECRETKEY",
            "APPLICATION__SECURITY__ENCRYPTION__KEY",
            "APPLICATION__SECURITY__ENCRYPTION__SALT"
        };

        foreach (var envVar in requiredEnvVars)
        {
            if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable(envVar)))
            {
                issues.Add($"Required environment variable '{envVar}' is not set.");
            }
        }
    }


}
