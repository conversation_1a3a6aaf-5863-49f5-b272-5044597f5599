using System.Security.Cryptography;
using System.Text;

namespace Curio.Infrastructure.Services;

public class TotpService : ITotpService
{
    private const int SecretLength = 32;
    private const int CodeLength = 6;
    private const int TimeStep = 30;

    public string GenerateSecret()
    {
        var bytes = new byte[SecretLength];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    public string GenerateCode(string secret)
    {
        var secretBytes = Convert.FromBase64String(secret);
        var timeCounter = GetTimeCounter();
        return GenerateTotp(secretBytes, timeCounter);
    }

    public bool ValidateCode(string secret, string code, int windowSize = 1)
    {
        if (string.IsNullOrEmpty(secret) || string.IsNullOrEmpty(code))
            return false;

        var secretBytes = Convert.FromBase64String(secret);
        var timeCounter = GetTimeCounter();

        // Check current time and surrounding windows
        for (int i = -windowSize; i <= windowSize; i++)
        {
            var testCode = GenerateTotp(secretBytes, timeCounter + i);
            if (testCode == code)
                return true;
        }

        return false;
    }

    public bool VerifyCode(string code, string secret, int windowSize = 1)
    {
        return ValidateCode(secret, code, windowSize);
    }

    public string GenerateQrCodeUri(string secret, string accountName, string issuer)
    {
        var secretBase32 = ConvertToBase32(Convert.FromBase64String(secret));
        return $"otpauth://totp/{Uri.EscapeDataString(issuer)}:{Uri.EscapeDataString(accountName)}?secret={secretBase32}&issuer={Uri.EscapeDataString(issuer)}";
    }

    public string GenerateQrCodeUrl(string accountName, string secret, string issuer = "Curio")
    {
        return GenerateQrCodeUri(secret, accountName, issuer);
    }

    public List<string> GenerateRecoveryCodes(int count = 10)
    {
        var codes = new List<string>();
        using var rng = RandomNumberGenerator.Create();

        for (int i = 0; i < count; i++)
        {
            var bytes = new byte[8];
            rng.GetBytes(bytes);
            var code = Convert.ToHexString(bytes).ToLower();
            // Format as XXXX-XXXX for readability
            codes.Add($"{code[..4]}-{code[4..]}");
        }

        return codes;
    }

    public bool VerifyRecoveryCode(string code, List<string> recoveryCodes)
    {
        if (string.IsNullOrEmpty(code) || recoveryCodes == null || !recoveryCodes.Any())
            return false;

        // Normalize the input code
        var normalizedInputCode = code.Replace("-", "").Replace(" ", "").ToLower();

        // Check if any recovery code matches
        return recoveryCodes.Any(recoveryCode =>
        {
            var normalizedRecoveryCode = recoveryCode.Replace("-", "").Replace(" ", "").ToLower();
            return normalizedRecoveryCode == normalizedInputCode;
        });
    }

    private static long GetTimeCounter()
    {
        var unixTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        return unixTime / TimeStep;
    }

    private static string GenerateTotp(byte[] secret, long timeCounter)
    {
        var timeBytes = BitConverter.GetBytes(timeCounter);
        if (BitConverter.IsLittleEndian)
            Array.Reverse(timeBytes);

        using var hmac = new HMACSHA1(secret);
        var hash = hmac.ComputeHash(timeBytes);

        var offset = hash[^1] & 0x0F;
        var code = ((hash[offset] & 0x7F) << 24) |
                   ((hash[offset + 1] & 0xFF) << 16) |
                   ((hash[offset + 2] & 0xFF) << 8) |
                   (hash[offset + 3] & 0xFF);

        var totp = code % (int)Math.Pow(10, CodeLength);
        return totp.ToString($"D{CodeLength}");
    }

    private static string ConvertToBase32(byte[] bytes)
    {
        const string alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        var result = new StringBuilder();

        for (int i = 0; i < bytes.Length; i += 5)
        {
            var chunk = new byte[5];
            Array.Copy(bytes, i, chunk, 0, Math.Min(5, bytes.Length - i));

            var value = (long)chunk[0] << 32 |
                       (long)chunk[1] << 24 |
                       (long)chunk[2] << 16 |
                       (long)chunk[3] << 8 |
                       chunk[4];

            for (int j = 7; j >= 0; j--)
            {
                if (i * 8 / 5 + j < bytes.Length * 8 / 5)
                {
                    result.Append(alphabet[(int)(value >> (j * 5)) & 0x1F]);
                }
            }
        }

        return result.ToString();
    }
}
