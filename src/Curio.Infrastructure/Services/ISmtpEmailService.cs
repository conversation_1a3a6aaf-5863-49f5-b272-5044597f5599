namespace Curio.Infrastructure.Services;

public interface ISmtpEmailService
{
    Task<bool> SendEmailAsync(EmailMessage message);
    Task<bool> SendTemplatedEmailAsync(string templateName, string to, string subject, object templateModel);
    Task<bool> SendBulkEmailAsync(IEnumerable<EmailMessage> messages);
}

public class EmailMessage
{
    public string To { get; set; } = string.Empty;
    public string? ToName { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string? PlainTextBody { get; set; }
    public string? HtmlBody { get; set; }
    public string? From { get; set; }
    public string? FromName { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public List<EmailAttachment> Attachments { get; set; } = new();
}

public class EmailAttachment
{
    public string FileName { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = "application/octet-stream";
}
