using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Curio.Infrastructure.Configuration;

namespace Curio.Infrastructure.Services;

public class SmtpEmailService : ISmtpEmailService, IDisposable
{
    private readonly EmailSettings _settings;
    private readonly IEmailTemplateService _templateService;
    private readonly ILogger<SmtpEmailService> _logger;
    private readonly SmtpClient _smtpClient;

    public SmtpEmailService(
        IOptions<EmailSettings> settings,
        IEmailTemplateService templateService,
        ILogger<SmtpEmailService> logger)
    {
        _settings = settings.Value;
        _templateService = templateService;
        _logger = logger;

        _smtpClient = CreateSmtpClient();
    }

    public async Task<bool> SendEmailAsync(EmailMessage message)
    {
        try
        {
            using var mailMessage = await CreateMailMessageAsync(message);

            await ExecuteWithRetryAsync(async () =>
            {
                await _smtpClient.SendMailAsync(mailMessage);
            });

            _logger.LogInformation("Email sent successfully to {To}", message.To);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {To}", message.To);
            return false;
        }
    }

    public async Task<bool> SendTemplatedEmailAsync(string templateName, string to, string subject, object templateModel)
    {
        try
        {
            if (!await _templateService.TemplateExistsAsync(templateName))
            {
                _logger.LogError("Email template {TemplateName} not found", templateName);
                return false;
            }

            var htmlBody = await _templateService.RenderTemplateAsync($"{templateName}.html", templateModel);
            string? plainTextBody = null;

            // Try to render plain text template if it exists
            if (await _templateService.TemplateExistsAsync($"{templateName}.txt"))
            {
                plainTextBody = await _templateService.RenderTemplateAsync($"{templateName}.txt", templateModel);
            }

            var message = new EmailMessage
            {
                To = to,
                Subject = subject,
                HtmlBody = htmlBody,
                PlainTextBody = plainTextBody
            };

            return await SendEmailAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send templated email {TemplateName} to {To}", templateName, to);
            return false;
        }
    }

    public async Task<bool> SendBulkEmailAsync(IEnumerable<EmailMessage> messages)
    {
        var results = new List<bool>();

        foreach (var message in messages)
        {
            var result = await SendEmailAsync(message);
            results.Add(result);

            // Small delay between bulk emails to avoid overwhelming SMTP server
            await Task.Delay(100);
        }

        var successCount = results.Count(r => r);
        var totalCount = results.Count;

        _logger.LogInformation("Bulk email completed: {SuccessCount}/{TotalCount} sent successfully",
            successCount, totalCount);

        return successCount == totalCount;
    }

    private SmtpClient CreateSmtpClient()
    {
        var client = new SmtpClient(_settings.Smtp.Host, _settings.Smtp.Port)
        {
            EnableSsl = _settings.Smtp.EnableSsl,
            UseDefaultCredentials = _settings.Smtp.UseDefaultCredentials,
            Timeout = _settings.Smtp.TimeoutSeconds * 1000
        };

        if (!_settings.Smtp.UseDefaultCredentials)
        {
            client.Credentials = new NetworkCredential(_settings.Smtp.Username, _settings.Smtp.Password);
        }

        return client;
    }

    private async Task<MailMessage> CreateMailMessageAsync(EmailMessage message)
    {
        var mailMessage = new MailMessage();

        // Set sender
        var fromEmail = message.From ?? _settings.DefaultSender.FromEmail;
        var fromName = message.FromName ?? _settings.DefaultSender.FromName;

        if (!string.IsNullOrEmpty(fromName))
        {
            mailMessage.From = new MailAddress(fromEmail, fromName);
        }
        else
        {
            mailMessage.From = new MailAddress(fromEmail);
        }

        // Set recipient
        if (!string.IsNullOrEmpty(message.ToName))
        {
            mailMessage.To.Add(new MailAddress(message.To, message.ToName));
        }
        else
        {
            mailMessage.To.Add(new MailAddress(message.To));
        }

        // Set reply-to if configured
        if (!string.IsNullOrEmpty(_settings.DefaultSender.ReplyToEmail))
        {
            var replyToName = _settings.DefaultSender.ReplyToName;
            if (!string.IsNullOrEmpty(replyToName))
            {
                mailMessage.ReplyToList.Add(new MailAddress(_settings.DefaultSender.ReplyToEmail, replyToName));
            }
            else
            {
                mailMessage.ReplyToList.Add(new MailAddress(_settings.DefaultSender.ReplyToEmail));
            }
        }

        mailMessage.Subject = message.Subject;

        // Set body content
        if (!string.IsNullOrEmpty(message.HtmlBody))
        {
            mailMessage.Body = message.HtmlBody;
            mailMessage.IsBodyHtml = true;

            // Add plain text alternative if available
            if (!string.IsNullOrEmpty(message.PlainTextBody))
            {
                var plainTextView = AlternateView.CreateAlternateViewFromString(
                    message.PlainTextBody, null, "text/plain");
                mailMessage.AlternateViews.Add(plainTextView);
            }
        }
        else if (!string.IsNullOrEmpty(message.PlainTextBody))
        {
            mailMessage.Body = message.PlainTextBody;
            mailMessage.IsBodyHtml = false;
        }

        // Add custom headers
        foreach (var header in message.Headers)
        {
            mailMessage.Headers.Add(header.Key, header.Value);
        }

        // Add attachments
        foreach (var attachment in message.Attachments)
        {
            var stream = new MemoryStream(attachment.Content);
            var mailAttachment = new Attachment(stream, attachment.FileName, attachment.ContentType);
            mailMessage.Attachments.Add(mailAttachment);
        }

        await Task.CompletedTask;
        return mailMessage;
    }

    private async Task ExecuteWithRetryAsync(Func<Task> operation)
    {
        var attempts = 0;
        var delay = _settings.Retry.DelayMilliseconds;

        while (attempts < _settings.Retry.MaxAttempts)
        {
            try
            {
                await operation();
                return;
            }
            catch (Exception ex) when (attempts < _settings.Retry.MaxAttempts - 1)
            {
                attempts++;
                _logger.LogWarning(ex, "Email send attempt {Attempt}/{MaxAttempts} failed, retrying in {Delay}ms",
                    attempts, _settings.Retry.MaxAttempts, delay);

                await Task.Delay(delay);

                if (_settings.Retry.ExponentialBackoff)
                {
                    delay *= 2;
                }
            }
        }
    }

    public void Dispose()
    {
        _smtpClient?.Dispose();
        GC.SuppressFinalize(this);
    }
}