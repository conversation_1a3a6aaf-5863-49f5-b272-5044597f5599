namespace Curio.Infrastructure.Services;

public interface ITotpService
{
    string GenerateSecret();
    string GenerateCode(string secret);
    bool ValidateCode(string secret, string code, int windowSize = 1);
    bool VerifyCode(string code, string secret, int windowSize = 1);
    string GenerateQrCodeUri(string secret, string accountName, string issuer);
    string GenerateQrCodeUrl(string accountName, string secret, string issuer = "Curio");
    List<string> GenerateRecoveryCodes(int count = 10);
    bool VerifyRecoveryCode(string code, List<string> recoveryCodes);
}
