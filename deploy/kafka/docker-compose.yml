networks:
  kafka-net:
    driver: bridge

# volumes:
#   zookeeper-data:
#     driver: local
#   zookeeper-logs:
#     driver: local
#   kafka-data:
#     driver: local

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.6.1
    container_name: zookeeper
    networks:
      - kafka-net
    volumes:
      - ./zookeeper-data:/var/lib/zookeeper/data
      - ./zookeeper-logs:/var/lib/zookeeper/log
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:7.6.1
    container_name: kafka
    networks:
      - kafka-net
    ports:
      - "9092:9092"
    volumes:
      - ./kafka-data:/var/lib/kafka/data
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
      KAFKA_LISTENERS: INTERNAL://0.0.0.0:29092,EXTERNAL://0.0.0.0:9092
      KAFKA_ADVERTISED_LISTENERS: INTERNAL://kafka:29092,EXTERNAL://localhost:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: INTERNAL
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_SASL_ENABLED_MECHANISMS: PLAIN

      # SASL配置
      KAFKA_LISTENER_NAME_EXTERNAL_SASL_ENABLED_MECHANISMS: PLAIN
      KAFKA_LISTENER_NAME_EXTERNAL_PLAIN_SASL_JAAS_CONFIG: 'org.apache.kafka.common.security.plain.PlainLoginModule required user_admin="admin123" user_producer="producer123" user_consumer="consumer123";'
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"

      # 可靠性配置 - 确保消息不丢失
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
      KAFKA_MIN_INSYNC_REPLICAS: 1
      KAFKA_UNCLEAN_LEADER_ELECTION_ENABLE: "false"

      # 日志保留配置 - Event Sourcing永久保存
      KAFKA_LOG_RETENTION_HOURS: -1 # 永久保留
      KAFKA_LOG_RETENTION_BYTES: -1 # 不限制大小
      KAFKA_LOG_SEGMENT_BYTES: 104857600 # 100MB

      # 性能优化配置
      KAFKA_NUM_NETWORK_THREADS: 3
      KAFKA_NUM_IO_THREADS: 8
      KAFKA_SOCKET_SEND_BUFFER_BYTES: 102400
      KAFKA_SOCKET_RECEIVE_BUFFER_BYTES: 102400
      KAFKA_SOCKET_REQUEST_MAX_BYTES: 104857600

      # 压缩配置
      KAFKA_COMPRESSION_TYPE: "snappy"

      # 批处理配置
      KAFKA_BATCH_SIZE: 16384
      KAFKA_LINGER_MS: 10

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    networks:
      - kafka-net
    ports:
      - "9093:8080"
    depends_on:
      - kafka
    environment:
      KAFKA_CLUSTERS_0_NAME: local-kafka
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
      # 作用和功能：
      # 动态配置启用 - 允许在 Kafka UI 界面中动态修改 Kafka 的配置
      # 实时配置管理 - 可以通过 Web 界面实时调整 Kafka 集群的参数
      # 无需重启 - 某些配置可以在不重启 Kafka 服务的情况下生效
      # 具体功能：
      # 在 Kafka UI 的 Web 界面中可以看到 "Config" 选项卡
      # 可以修改 topic 级别的配置（如保留时间、压缩设置等）
      # 可以调整 broker 级别的动态配置
      # 提供配置历史记录和回滚功能
      # 安全注意事项：
      # 启用此功能意味着任何有权访问 Kafka UI 的用户都可以修改 Kafka 配置
      # 在生产环境中建议谨慎使用，或者配合访问控制
      # 建议只在开发和测试环境中启用
      DYNAMIC_CONFIG_ENABLED: "true"
