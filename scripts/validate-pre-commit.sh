#!/bin/bash

# Validation script for pre-commit setup
# This script validates that all pre-commit dependencies are properly installed

set -e

echo "🔍 Validating pre-commit setup for Curio API project..."

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if command exists
check_command() {
    if command -v "$1" &> /dev/null; then
        echo -e "${GREEN}✅ $1 is installed${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 is not installed${NC}"
        return 1
    fi
}

# Function to check if file exists
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1 exists${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 is missing${NC}"
        return 1
    fi
}

echo ""
echo "📋 Checking required tools..."

# Check required commands
MISSING_TOOLS=0

check_command "python3" || MISSING_TOOLS=$((MISSING_TOOLS + 1))
check_command "pip3" || MISSING_TOOLS=$((MISSING_TOOLS + 1))
check_command "dotnet" || MISSING_TOOLS=$((MISSING_TOOLS + 1))
check_command "git" || MISSING_TOOLS=$((MISSING_TOOLS + 1))
check_command "pre-commit" || MISSING_TOOLS=$((MISSING_TOOLS + 1))

echo ""
echo "📁 Checking configuration files..."

# Check required files
MISSING_FILES=0

check_file ".pre-commit-config.yaml" || MISSING_FILES=$((MISSING_FILES + 1))
check_file ".secrets.baseline" || MISSING_FILES=$((MISSING_FILES + 1))
check_file ".editorconfig" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "Directory.Build.props" || MISSING_FILES=$((MISSING_FILES + 1))
check_file ".gitattributes" || MISSING_FILES=$((MISSING_FILES + 1))

echo ""
echo "🔧 Checking .NET tools..."

# Check .NET format
if dotnet format --version &> /dev/null; then
    echo -e "${GREEN}✅ dotnet-format is available${NC}"
else
    echo -e "${YELLOW}⚠️  dotnet-format not found globally, but may work locally${NC}"
fi

# Check if solution builds
echo ""
echo "🏗️  Testing solution build..."
if dotnet build Curio.sln --no-restore > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Solution builds successfully${NC}"
else
    echo -e "${RED}❌ Solution build failed${NC}"
    MISSING_TOOLS=$((MISSING_TOOLS + 1))
fi

echo ""
echo "🪝 Checking pre-commit installation..."

# Check if pre-commit is installed in git
if [ -f ".git/hooks/pre-commit" ]; then
    echo -e "${GREEN}✅ Pre-commit hooks are installed in git${NC}"
else
    echo -e "${YELLOW}⚠️  Pre-commit hooks not installed. Run 'pre-commit install'${NC}"
fi

echo ""
echo "🧪 Testing pre-commit configuration..."

# Test pre-commit configuration
if pre-commit run --all-files --dry-run > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Pre-commit configuration is valid${NC}"
else
    echo -e "${RED}❌ Pre-commit configuration has issues${NC}"
    MISSING_TOOLS=$((MISSING_TOOLS + 1))
fi

echo ""
echo "📊 Validation Summary:"
echo "====================="

if [ $MISSING_TOOLS -eq 0 ] && [ $MISSING_FILES -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! Pre-commit is ready to use.${NC}"
    echo ""
    echo "Next steps:"
    echo "• Make a test commit to verify hooks work"
    echo "• Run 'pre-commit run --all-files' to test all files"
    echo "• Check the documentation in docs/Pre-commit-Setup-Guide.md"
    exit 0
else
    echo -e "${RED}❌ Validation failed:${NC}"
    [ $MISSING_TOOLS -gt 0 ] && echo "  • $MISSING_TOOLS required tools are missing"
    [ $MISSING_FILES -gt 0 ] && echo "  • $MISSING_FILES configuration files are missing"
    echo ""
    echo "Please run './scripts/setup-pre-commit.sh' to fix these issues."
    exit 1
fi
