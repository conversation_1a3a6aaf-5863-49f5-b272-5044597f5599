#!/bin/bash

# Curio API Local Environment Setup Script
# This script helps you set up your local development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
API_DIR="$PROJECT_ROOT/src/Curio.Api"

echo -e "${BLUE}🚀 Curio API Local Environment Setup${NC}"
echo "=================================================="

# Function to generate random string
generate_random_string() {
    local length=$1
    openssl rand -base64 $((length * 3 / 4)) | tr -d "=+/" | cut -c1-${length}
}

# Function to prompt for input with default
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local result
    
    read -p "$prompt [$default]: " result
    echo "${result:-$default}"
}

# Check if .env already exists
if [ -f "$API_DIR/.env" ]; then
    echo -e "${YELLOW}⚠️  .env file already exists!${NC}"
    read -p "Do you want to overwrite it? (y/N): " overwrite
    if [[ ! $overwrite =~ ^[Yy]$ ]]; then
        echo -e "${GREEN}✅ Keeping existing .env file${NC}"
        exit 0
    fi
fi

echo -e "${GREEN}📝 Setting up environment variables...${NC}"
echo

# Database configuration
echo -e "${BLUE}Database Configuration:${NC}"
DB_HOST=$(prompt_with_default "Database host" "localhost")
DB_PORT=$(prompt_with_default "Database port" "5432")
DB_NAME=$(prompt_with_default "Database name" "curio")
DB_USER=$(prompt_with_default "Database username" "curio")
DB_PASSWORD=$(prompt_with_default "Database password" "curio123")

echo

# Security configuration
echo -e "${BLUE}Security Configuration:${NC}"
echo "Generating secure random keys..."

JWT_SECRET=$(generate_random_string 64)
ENCRYPTION_KEY=$(generate_random_string 32)
ENCRYPTION_SALT=$(generate_random_string 16)

echo -e "${GREEN}✅ Generated JWT secret key (64 chars)${NC}"
echo -e "${GREEN}✅ Generated encryption key (32 chars)${NC}"
echo -e "${GREEN}✅ Generated encryption salt (16 chars)${NC}"

echo

# Email configuration (optional)
echo -e "${BLUE}Email Configuration (optional):${NC}"
read -p "Do you want to configure email? (y/N): " configure_email

EMAIL_USERNAME=""
EMAIL_PASSWORD=""
EMAIL_FROM=""

if [[ $configure_email =~ ^[Yy]$ ]]; then
    EMAIL_USERNAME=$(prompt_with_default "SMTP username (email)" "")
    EMAIL_PASSWORD=$(prompt_with_default "SMTP password" "")
    EMAIL_FROM=$(prompt_with_default "From email address" "$EMAIL_USERNAME")
fi

echo

# Application configuration
echo -e "${BLUE}Application Configuration:${NC}"
API_BASE_URL=$(prompt_with_default "API base URL" "https://localhost:7274")

echo

# Create .env file
echo -e "${GREEN}📄 Creating .env file...${NC}"

cat > "$API_DIR/.env" << EOF
# Curio API Local Environment Configuration
# Generated on $(date)

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
ASPNETCORE_ENVIRONMENT=Development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE__HOST=$DB_HOST
DATABASE__PORT=$DB_PORT
DATABASE__DATABASE=$DB_NAME
DATABASE__USERNAME=$DB_USER
DATABASE__PASSWORD=$DB_PASSWORD

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
APPLICATION__SECURITY__JWT__SECRETKEY=$JWT_SECRET
APPLICATION__SECURITY__ENCRYPTION__KEY=$ENCRYPTION_KEY
APPLICATION__SECURITY__ENCRYPTION__SALT=$ENCRYPTION_SALT

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APPLICATION__API__BASEURL=$API_BASE_URL

# CORS Origins for local development
APPLICATION__API__CORS__ALLOWEDORIGINS__0=http://localhost:3000
APPLICATION__API__CORS__ALLOWEDORIGINS__1=http://localhost:5173
APPLICATION__API__CORS__ALLOWEDORIGINS__2=https://localhost:7274

EOF

# Add email configuration if provided
if [[ $configure_email =~ ^[Yy]$ ]] && [ ! -z "$EMAIL_USERNAME" ]; then
cat >> "$API_DIR/.env" << EOF

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL__SMTP__USERNAME=$EMAIL_USERNAME
EMAIL__SMTP__PASSWORD=$EMAIL_PASSWORD
EMAIL__DEFAULTSENDER__FROMEMAIL=$EMAIL_FROM

EOF
fi

# Add optional configurations
cat >> "$API_DIR/.env" << EOF

# =============================================================================
# OPTIONAL CONFIGURATIONS
# =============================================================================
# Uncomment and modify as needed:

# For different Orleans cluster:
# ORLEANS__CLUSTERID=curio-cluster-local
# ORLEANS__SERVICEID=curio-service-local

# For Kafka authentication (if needed):
# KAFKA__SASLUSERNAME=your-kafka-username
# KAFKA__SASLPASSWORD=your-kafka-password

# For different Kafka brokers:
# KAFKA__BROKERLIST__0=localhost:9092
EOF

echo -e "${GREEN}✅ .env file created successfully!${NC}"
echo

# Show next steps
echo -e "${BLUE}🎉 Setup Complete!${NC}"
echo "=================================================="
echo -e "${GREEN}Next steps:${NC}"
echo "1. Review the generated .env file: $API_DIR/.env"
echo "2. Make sure your database is running and accessible"
echo "3. Run the application:"
echo "   cd $PROJECT_ROOT"
echo "   dotnet run --project src/Curio.Api"
echo
echo -e "${YELLOW}💡 Tips:${NC}"
echo "• The .env file is automatically ignored by git"
echo "• You can modify the .env file anytime to change configuration"
echo "• For different local setups, copy .env to .env.docker or .env.cloud"
echo
echo -e "${GREEN}🔒 Security:${NC}"
echo "• Strong random keys have been generated for you"
echo "• Never commit the .env file to version control"
echo "• Rotate keys regularly in production"
