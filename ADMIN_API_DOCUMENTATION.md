# Curio Admin 后台管理系统 API 文档

本文档为 Curio Admin 后台管理系统的完整 API 接口文档，基于 Orleans + Event Sourcing + CQRS 架构实现。

## 目录

1. [系统概述](#系统概述)
2. [技术架构](#技术架构)
3. [认证与授权](#认证与授权)
4. [API 端点列表](#api-端点列表)
5. [数据模型](#数据模型)
6. [错误处理](#错误处理)
7. [部署配置](#部署配置)

## 系统概述

### 核心功能

- **管理员管理**：支持管理员的增删改查、状态管理
- **2FA 认证**：基于 TOTP 的双因子认证，支持 Google Authenticator 等
- **RBAC 权限**：基于角色的访问控制，灵活的权限管理
- **审计日志**：完整的操作审计和安全监控
- **密码策略**：强密码要求、定期更换提醒

### 技术特性

- **分布式架构**：Orleans Virtual Actor 模型
- **事件驱动**：Event Sourcing + CQRS 模式
- **高可用性**：支持集群部署和负载均衡
- **安全认证**：JWT + 2FA 双重保护

## 技术架构

### 架构模式

```
Web Admin Frontend
        ↓
   Admin API Layer
        ↓
  Application Services
        ↓
   Orleans Grains (Business Logic)
        ↓
Event Store (Kafka) + State Storage (PostgreSQL)
```

### 核心组件

- **AdminGrain**: 管理员业务逻辑
- **RoleGrain**: 角色权限管理
- **PermissionQueryGrain**: 权限查询和缓存
- **AuditGrain**: 审计日志记录
- **TotpService**: 2FA 认证服务

## 认证与授权

### JWT Token 结构

```json
{
  "sub": "admin_id",
  "admin_id": "admin123",
  "username": "<EMAIL>",
  "roles": ["SuperAdmin", "SystemAdmin"],
  "permissions": ["Users:Read", "Admins:Manage"],
  "exp": 1234567890,
  "iat": 1234567890
}
```

### 权限模型

```
资源 (Resource) + 操作 (Action) = 权限
例如：Users:Read, Admins:Create, Roles:Delete
```

## API 端点列表

### 1. 认证相关接口

#### 1.1 管理员登录

```http
POST /api/admin/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "strongPassword123",
  "twoFactorCode": "123456",
  "ipAddress": "*************",
  "userAgent": "Mozilla/5.0..."
}
```

**响应示例：**

```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "success": true,
    "admin": {
      "id": "admin123",
      "username": "<EMAIL>",
      "name": "System Administrator",
      "status": "Active",
      "twoFactorEnabled": true,
      "roles": ["SuperAdmin"]
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "dGhpc19pc19yZWZyZXNo...",
    "expiresAt": "2024-01-01T12:00:00Z"
  }
}
```

#### 1.2 二步验证

```http
POST /api/admin/verify-2fa
Content-Type: application/json

{
  "adminId": "admin123",
  "code": "123456",
  "loginSessionId": "session123"
}
```

### 2. 管理员管理接口

#### 2.1 创建管理员

```http
POST /api/admin
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "<EMAIL>",
  "email": "<EMAIL>",
  "name": "New Administrator",
  "initialPassword": "TempPass123!",
  "roleIds": ["role1", "role2"]
}
```

#### 2.2 获取管理员信息

```http
GET /api/admin/{adminId}
Authorization: Bearer <token>
```

#### 2.3 更新管理员

```http
PUT /api/admin/{adminId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "Updated Name"
}
```

#### 2.4 修改密码

```http
POST /api/admin/{adminId}/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword456",
  "isInitialSetup": false
}
```

#### 2.5 重置密码

```http
POST /api/admin/{adminId}/reset-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "newPassword": "resetPassword789"
}
```

#### 2.6 更新管理员状态

```http
PUT /api/admin/{adminId}/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "newStatus": "Suspended",
  "reason": "Security review"
}
```

#### 2.7 解锁账户

```http
POST /api/admin/{adminId}/unlock
Authorization: Bearer <token>
```

### 3. 二步认证管理

#### 3.1 启用 2FA

```http
POST /api/admin/{adminId}/enable-2fa
Authorization: Bearer <token>
Content-Type: application/json

{
  "verificationCode": "123456"
}
```

**响应示例：**

```json
{
  "success": true,
  "data": {
    "success": true,
    "secretKey": "JBSWY3DPEHPK3PXP",
    "qrCodeUrl": "otpauth://totp/CurioAdmin:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=CurioAdmin",
    "recoveryCodes": ["12345-67890", "abcde-fghij", "..."]
  }
}
```

#### 3.2 禁用 2FA

```http
POST /api/admin/{adminId}/disable-2fa
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "currentPassword123",
  "reason": "Device lost"
}
```

#### 3.3 重新生成恢复代码

```http
POST /api/admin/{adminId}/regenerate-recovery-codes
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "currentPassword123"
}
```

#### 3.4 获取剩余恢复代码数量

```http
GET /api/admin/{adminId}/recovery-codes-count
Authorization: Bearer <token>
```

### 4. 角色管理接口

#### 4.1 创建角色

```http
POST /api/admin/roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "roleName": "BusinessAdmin",
  "description": "业务管理员",
  "permissions": [
    {
      "resource": "Users",
      "action": "Read"
    },
    {
      "resource": "Users",
      "action": "Create"
    }
  ]
}
```

#### 4.2 获取角色信息

```http
GET /api/admin/roles/{roleId}
Authorization: Bearer <token>
```

#### 4.3 更新角色

```http
PUT /api/admin/roles/{roleId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "roleName": "Updated Role Name",
  "description": "Updated description"
}
```

#### 4.4 删除角色

```http
DELETE /api/admin/roles/{roleId}
Authorization: Bearer <token>
```

#### 4.5 分配权限到角色

```http
POST /api/admin/roles/{roleId}/permissions
Authorization: Bearer <token>
Content-Type: application/json

{
  "resource": "Admins",
  "action": "Create"
}
```

#### 4.6 批量分配权限

```http
POST /api/admin/roles/{roleId}/permissions/batch-assign
Authorization: Bearer <token>
Content-Type: application/json

{
  "permissions": [
    {
      "resource": "Users",
      "action": "Read"
    },
    {
      "resource": "Users",
      "action": "Update"
    }
  ]
}
```

#### 4.7 搜索角色

```http
GET /api/admin/roles?keyword=admin&isActive=true&skip=0&take=20
Authorization: Bearer <token>
```

### 5. 权限查询接口

#### 5.1 检查管理员权限

```http
POST /api/admin/{adminId}/check-permission
Authorization: Bearer <token>
Content-Type: application/json

{
  "resource": "Users",
  "action": "Create"
}
```

#### 5.2 获取管理员权限列表

```http
GET /api/admin/{adminId}/permissions
Authorization: Bearer <token>
```

#### 5.3 获取管理员角色列表

```http
GET /api/admin/{adminId}/roles
Authorization: Bearer <token>
```

#### 5.4 获取所有可用权限

```http
GET /api/admin/roles/permissions/available
Authorization: Bearer <token>
```

### 6. 搜索和统计接口

#### 6.1 搜索管理员

```http
POST /api/admin/search
Authorization: Bearer <token>
Content-Type: application/json

{
  "keyword": "admin",
  "status": "Active",
  "roleIds": ["role1"],
  "twoFactorEnabled": true,
  "createdAfter": "2024-01-01T00:00:00Z",
  "skip": 0,
  "take": 20,
  "sortBy": "CreatedAt",
  "sortDescending": true
}
```

#### 6.2 获取管理员统计

```http
GET /api/admin/stats
Authorization: Bearer <token>
```

**响应示例：**

```json
{
  "success": true,
  "data": {
    "totalAdmins": 50,
    "activeAdmins": 45,
    "lockedAdmins": 3,
    "adminsWithTwoFactor": 40,
    "pendingSetupAdmins": 2,
    "lastUpdated": "2024-01-01T12:00:00Z"
  }
}
```

## 数据模型

### AdminDto

```typescript
interface AdminDto {
  id: string;
  username: string;
  email: string;
  name: string;
  status: AdminStatus;
  createdAt: string;
  lastLoginAt?: string;
  updatedAt?: string;
  createdBy: string;
  roleIds: string[];
  roles: RoleDto[];
  twoFactorEnabled: boolean;
  twoFactorEnabledAt?: string;
  failedLoginAttempts: number;
  isLocked: boolean;
  mustChangePassword: boolean;
  lastLoginIp?: string;
  effectivePermissions: PermissionDto[];
}
```

### RoleDto

```typescript
interface RoleDto {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt?: string;
  createdBy: string;
  isActive: boolean;
  permissions: PermissionDto[];
  permissionCount: number;
  isBuiltIn: boolean;
  assignedAdminCount: number;
}
```

### PermissionDto

```typescript
interface PermissionDto {
  resource: PermissionResource;
  action: PermissionAction;
  resourceName: string;
  actionName: string;
  description: string;
  assignedAt: string;
  assignedBy: string;
  grantedByRole?: string;
}
```

### 枚举类型

```typescript
enum AdminStatus {
  Active = "Active",
  Disabled = "Disabled",
  Locked = "Locked",
  PendingSetup = "PendingSetup",
  Suspended = "Suspended",
}

enum PermissionResource {
  Users = "Users",
  Admins = "Admins",
  Roles = "Roles",
  Permissions = "Permissions",
  SystemConfig = "SystemConfig",
  AuditLogs = "AuditLogs",
  EmailTemplates = "EmailTemplates",
  Monitoring = "Monitoring",
  ProjectionManagement = "ProjectionManagement",
  DeadLetterQueue = "DeadLetterQueue",
}

enum PermissionAction {
  Read = "Read",
  Create = "Create",
  Update = "Update",
  Delete = "Delete",
  Execute = "Execute",
  Manage = "Manage",
}
```

## 错误处理

### 统一响应格式

```typescript
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  code: number;
  timestamp: string;
}
```

### 常见错误码

```typescript
const ApiCodes = {
  Success: 200,
  Created: 201,
  Deleted: 204,
  ValidationFailed: 400,
  Unauthorized: 401,
  InsufficientPermissions: 403,
  ResourceNotFound: 404,
  BusinessRuleValidationFailed: 422,
  InternalServerError: 500,
};
```

### 错误响应示例

```json
{
  "success": false,
  "message": "Insufficient permissions. Required: Admins:Create",
  "code": 403,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 部署配置

### 环境变量

```bash
# 数据库配置
DATABASE_CONNECTION_STRING="Host=localhost;Database=curio-api;Username=********;Password=********"

# Kafka配置
KAFKA_BROKERS="localhost:9092"
KAFKA_SECURITY_PROTOCOL="SASL_PLAINTEXT"
KAFKA_SASL_USERNAME="admin"
KAFKA_SASL_PASSWORD="admin123"

# JWT配置
JWT_SECRET_KEY="your-secret-key-here"
JWT_EXPIRES_IN_HOURS=8
JWT_REFRESH_EXPIRES_IN_DAYS=30

# Orleans配置
ORLEANS_CLUSTER_ID="curio-cluster"
ORLEANS_SERVICE_ID="curio-admin"
```

### Docker Compose

```yaml
version: "3.8"
services:
  curio-api:
    build: .
    ports:
      - "5000:5000"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    depends_on:
      - ********ql
      - kafka

  orleans-silo:
    build: .
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    depends_on:
      - ********ql
      - kafka
```

## 前端集成指南

### 1. 认证流程

```typescript
// 登录
const loginResponse = await fetch("/api/admin/login", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    username: "<EMAIL>",
    password: "password123",
  }),
});

// 存储Token
const { accessToken, refreshToken } = loginResponse.data;
localStorage.setItem("accessToken", accessToken);
localStorage.setItem("refreshToken", refreshToken);

// 后续请求携带Token
const apiRequest = await fetch("/api/admin/users", {
  headers: {
    Authorization: `Bearer ${accessToken}`,
    "Content-Type": "application/json",
  },
});
```

### 2. 权限验证

```typescript
// 检查权限
const hasPermission = await checkPermission("Users", "Create");
if (hasPermission) {
  // 显示创建按钮
}

// 权限检查函数
async function checkPermission(
  resource: string,
  action: string
): Promise<boolean> {
  const response = await fetch(
    `/api/admin/${currentAdminId}/check-permission`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ resource, action }),
    }
  );

  return response.data.hasPermission;
}
```

### 3. 2FA 集成

```typescript
// 启用2FA流程
async function enable2FA(verificationCode: string) {
  const response = await fetch(`/api/admin/${adminId}/enable-2fa`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ verificationCode }),
  });

  if (response.data.success) {
    // 显示QR码和恢复代码
    displayQRCode(response.data.qrCodeUrl);
    showRecoveryCodes(response.data.recoveryCodes);
  }
}
```

## 测试指南

### 单元测试

```bash
# 运行所有测试
dotnet test

# 运行特定测试项目
dotnet test tests/Curio.UnitTests

# 生成覆盖率报告
dotnet test --collect:"XPlat Code Coverage"
```

### API 测试

```bash
# 使用curl测试登录
curl -X POST http://localhost:5000/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password123"}'

# 测试权限验证
curl -X GET http://localhost:5000/api/admin/admin123 \
  -H "Authorization: Bearer <token>"
```

## 监控和日志

### 健康检查端点

```http
GET /health
GET /health/ready
GET /health/live
```

### 日志记录

- **审计日志**：所有管理员操作
- **安全日志**：登录尝试、权限检查
- **性能日志**：API 响应时间、Orleans 性能指标
- **错误日志**：异常和错误信息

### 监控指标

- API 响应时间
- 登录成功/失败率
- 2FA 验证成功率
- 权限检查性能
- Orleans Grain 激活数量

---

## 总结

本 Admin 后台管理系统提供了完整的管理员管理、角色权限控制、2FA 认证等功能，基于现代化的微服务架构设计，具备高可用、高安全性的特点。

前端团队可以基于此 API 文档进行对接开发，实现功能完善的管理后台界面。系统支持横向扩展，可根据实际需求进行部署配置调整。

如有任何问题，请联系开发团队。
