# Set default behavior to automatically normalize line endings
* text=auto

# Force batch scripts to always use CRLF line endings so that if a repo is accessed
# in Windows via a file share from Linux, the scripts will work.
*.{cmd,[cC][mM][dD]} text eol=crlf
*.{bat,[bB][aA][tT]} text eol=crlf

# Force bash scripts to always use LF line endings so that if a repo is accessed
# in Unix via a file share from Windows, the scripts will work.
*.sh text eol=lf

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.cs text diff=csharp
*.csx text diff=csharp
*.vb text
*.vbx text
*.resx text
*.c text
*.cpp text
*.cxx text
*.h text
*.hxx text
*.py text diff=python
*.pyw text diff=python
*.pyx text diff=python
*.pxd text diff=python
*.pxi text diff=python

# Declare files that will always have CRLF line endings on checkout.
*.sln text eol=crlf

# Declare files that will always have LF line endings on checkout.
*.{json,js,jsx,ts,tsx} text eol=lf
*.{yml,yaml} text eol=lf
*.{md,markdown,mdown,mkd} text eol=lf
*.{htm,html,xhtml} text eol=lf
*.{css,scss,sass,less} text eol=lf
*.{xml,config,props,targets,nuspec,resx,ruleset,vsixmanifest,vsct} text eol=lf

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pyc binary
*.pdf binary
*.ez binary
*.bz2 binary
*.swp binary
*.cache binary
*.dll binary
*.exe binary
*.so binary
*.dylib binary

# Visual Studio
*.sln text eol=crlf
*.csproj text
*.vbproj text
*.vcxproj text
*.vcproj text
*.dbproj text
*.fsproj text
*.lsproj text
*.wixproj text
*.modelproj text
*.sqlproj text
*.wwaproj text

# Visual Studio Solution Files
*.sln text eol=crlf
*.suo binary
*.user binary
*.userosscache binary
*.sln.docstates binary

# Build results
[Dd]ebug/ binary
[Dd]ebugPublic/ binary
[Rr]elease/ binary
[Rr]eleases/ binary
x64/ binary
x86/ binary
build/ binary
bld/ binary
[Bb]in/ binary
[Oo]bj/ binary
