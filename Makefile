# Makefile for Curio API project
# Provides common development tasks and pre-commit related commands

.PHONY: help setup-precommit validate-precommit format build test clean restore run

# Default target
help:
	@echo "Available targets:"
	@echo "  setup-precommit    - Install and configure pre-commit hooks"
	@echo "  validate-precommit - Validate pre-commit setup"
	@echo "  format            - Format code using dotnet format"
	@echo "  build             - Build the solution"
	@echo "  test              - Run all tests"
	@echo "  clean             - Clean build artifacts"
	@echo "  restore           - Restore NuGet packages"
	@echo "  run               - Run the API project"
	@echo "  precommit-run     - Run pre-commit on all files"
	@echo "  precommit-update  - Update pre-commit hooks"

# Pre-commit related targets
setup-precommit:
	@echo "Setting up pre-commit hooks..."
	./scripts/setup-pre-commit.sh

validate-precommit:
	@echo "Validating pre-commit setup..."
	./scripts/validate-pre-commit.sh

precommit-run:
	@echo "Running pre-commit on all files..."
	pre-commit run --all-files

precommit-update:
	@echo "Updating pre-commit hooks..."
	pre-commit autoupdate

# .NET development targets
format:
	@echo "Formatting code..."
	dotnet format

build:
	@echo "Building solution..."
	dotnet build

test:
	@echo "Running tests..."
	dotnet test

clean:
	@echo "Cleaning build artifacts..."
	dotnet clean

restore:
	@echo "Restoring NuGet packages..."
	dotnet restore

run:
	@echo "Running API project..."
	dotnet run --project src/Curio.Api

# Combined targets
check: format build test
	@echo "All checks completed successfully!"

ci: restore build test precommit-run
	@echo "CI pipeline completed successfully!"
